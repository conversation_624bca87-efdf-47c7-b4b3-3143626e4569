# RMS2018 快速测试配置指南

## 概述

本指南帮助你快速配置RMS2018应用程序，连接到测试数据库192.168.2.14，并绕过登录验证，以便直接测试房间转移计费功能。

## 已完成的配置

### 1. 数据库连接配置

**文件**: `RMS2018/App.config`
```xml
<connectionStrings>
  <!-- 测试数据库连接 - 指向192.168.2.14 -->
  <add name="dbfoodConn" connectionString="Data Source=192.168.2.14;Initial Catalog=dbfood;User ID=sa;Password=************"/>
  <add name="rms2019Conn" connectionString="Data Source=192.168.2.14;Initial Catalog=RMS2019;User ID=sa;Password=************"/>
</connectionStrings>

<appSettings>
  <add key="TestMode" value="true"/>
  <add key="RmsServerName" value="192.168.2.14"/>
  <add key="shopid" value="1"/>
  <add key="AutoLogin" value="true"/>
  <add key="TestUserId" value="admin"/>
</appSettings>
```

**文件**: `RMSDao/DbHelp_rms2019.cs`
```csharp
// 直接使用测试服务器地址
string rmsServerName = "192.168.2.14";

// 构建连接字符串 - 使用大写的RMS2019数据库名
dao.connstr = $"Data Source={rmsServerName};Initial Catalog=RMS2019;User ID=sa;Password=************";
```

### 2. 自动登录功能

**文件**: `RMS2018/WLogin.xaml.cs`
- 添加了`CheckAutoLogin()`方法
- 在构造函数中自动调用自动登录检查
- 当`AutoLogin=true`时，自动使用测试用户登录

### 3. 程序启动修改

**文件**: `RMS2018/Program.cs`
- 修改了参数检查逻辑，测试模式下允许无参数启动
- 自动设置测试环境的默认值

## 启动方法

### 方法一：使用批处理文件（推荐）

1. **双击运行**: `RMS2018/StartTestMode.bat`
2. **系统会自动**:
   - 连接到192.168.2.14数据库
   - 启用自动登录
   - 进入主界面

### 方法二：直接启动应用程序

1. **编译项目**
2. **运行**: `RMS2018.exe test`（传递任意参数）
3. **或者**: 直接运行（如果TestMode=true）

### 方法三：使用测试启动器

1. **编译时设置**: 将`TestLauncher.cs`设为启动项目
2. **直接运行**: 无需参数，自动进入测试模式

## 测试步骤

### 1. 启动应用程序

- 使用上述任一方法启动应用程序
- 系统会显示测试模式提示信息
- 自动登录并进入主界面

### 2. 打开转房界面

- 在主界面中找到"转房"功能
- 点击进入转房界面

### 3. 测试免费升级功能

1. **选择转出房间**: 选择一个已占用的房间
2. **选择转入房间**: 选择一个空闲的更高级房间
3. **选择计费方式**: 选择"按原房间类型计费（免费升级）"
4. **执行转房**: 点击转房按钮
5. **验证结果**: 
   - 客户物理位置更新
   - 计费标准保持原房间类型

### 4. 测试付费升级功能

1. **选择转出房间**: 选择一个已占用的房间
2. **选择转入房间**: 选择一个空闲的更高级房间
3. **选择计费方式**: 选择"按新房间类型计费（付费升级）"
4. **执行转房**: 点击转房按钮
5. **验证结果**: 
   - 客户物理位置更新
   - 计费标准更新为新房间类型

## 验证方法

### 1. 界面验证

- 确认转房界面显示单选按钮选择
- 确认默认选择"免费升级"
- 确认可以切换选择

### 2. 数据库验证

连接到192.168.2.14数据库，执行以下查询：

```sql
-- 查看转房前后的数据变化
SELECT Ikey, RmNo, RtNo, RtName, FromRmNo 
FROM OpenCacheInfo 
WHERE ShopId = 1;

-- 查看转房记录
SELECT * FROM RmExchange 
WHERE ShopId = 1 
ORDER BY ExchangeDate DESC;
```

### 3. 日志验证

查看系统日志，确认：
- 免费升级：显示"房转房操作成功（免费升级）"
- 付费升级：显示"房转房操作成功（付费升级）"

## 故障排除

### 问题1：数据库连接失败

**可能原因**:
- 网络连接问题
- 数据库服务器未启动
- 用户名密码错误

**解决方案**:
1. 检查网络连接到192.168.2.14
2. 确认数据库服务器状态
3. 验证用户名密码：sa/************

### 问题2：登录失败

**可能原因**:
- 自动登录配置错误
- 用户表中没有测试用户

**解决方案**:
1. 检查App.config中的AutoLogin设置
2. 在数据库中创建测试用户
3. 或者手动输入用户名密码登录

### 问题3：转房功能不可用

**可能原因**:
- 没有房间数据
- 权限不足
- 存储过程不存在

**解决方案**:
1. 确认数据库中有房间数据
2. 检查用户权限
3. 确认存储过程已创建

## 配置文件说明

### App.config 关键配置项

```xml
<!-- 启用测试模式 -->
<add key="TestMode" value="true"/>

<!-- 数据库服务器地址 -->
<add key="RmsServerName" value="192.168.2.14"/>

<!-- 门店ID -->
<add key="shopid" value="1"/>

<!-- 启用自动登录 -->
<add key="AutoLogin" value="true"/>

<!-- 测试用户ID -->
<add key="TestUserId" value="admin"/>
```

## 注意事项

1. **仅用于测试**: 此配置仅用于开发和测试环境
2. **数据安全**: 不要在生产环境中使用这些设置
3. **密码安全**: 测试完成后及时修改密码
4. **数据备份**: 测试前备份重要数据

## 联系支持

如果遇到问题，请：
1. 检查网络连接
2. 确认数据库状态
3. 查看错误日志
4. 联系开发团队
