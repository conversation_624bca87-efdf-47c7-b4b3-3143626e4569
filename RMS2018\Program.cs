﻿using RMSBLL.DbFood;
using RMSUtils;
using RMSUtils.RMS;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;

namespace RMS2018
{
    class Program
    {
        [STAThread]
        static void Main(string[] args)
        {
           


            // 检查是否为调试模式或测试模式
            bool isDebugMode = System.Diagnostics.Debugger.IsAttached;
            bool isTestMode = System.Configuration.ConfigurationManager.AppSettings["TestMode"] == "true";

            if (args.Length == 0 && !isDebugMode && !isTestMode)
            {
                MessageBox.Show("程序无法直接启动!", "提示", MessageBoxButton.OK, MessageBoxImage.Error);
                return;
            }

            // 在调试模式下设置默认参数
            if (isDebugMode && args.Length == 0)
            {
                // 模拟传入参数，避免后续代码出错
                args = new string[] { "debug" };
            }
            string conent = Regedit.GetVal("Activation_rms");

            // 在调试模式或测试模式下设置默认值
            if (isDebugMode || isTestMode)
            {
                if (string.IsNullOrEmpty(conent) || conent == "no")
                {
                    conent = "yes";
                    Regedit.SetVal("Activation_rms", "yes");
                }

                string shopId = Regedit.GetVal("ShopId");
                if (string.IsNullOrEmpty(shopId) || shopId == "no" || shopId == "0")
                {
                    shopId = System.Configuration.ConfigurationManager.AppSettings["shopid"] ?? "1";
                    Regedit.SetVal("ShopId", shopId);
                }

                // 显示调试模式提示
                if (isDebugMode)
                {
                    MessageBox.Show(
                        "调试模式已启用：\n" +
                        "• 数据库服务器：192.168.2.14\n" +
                        "• 数据库名称：RMS2019\n" +
                        "• 门店ID：" + shopId + "\n" +
                        "• 自动登录：已配置\n\n" +
                        "系统将自动进入测试环境。",
                        "调试模式",
                        MessageBoxButton.OK,
                        MessageBoxImage.Information
                    );
                }
            }

            RMS2018.App app = new RMS2018.App();
            //  app.InitializeComponent();
            System.Uri resourceLocater = new System.Uri("/RMS2018;component/app.xaml", System.UriKind.Relative);

#line 1 "..\..\App.xaml"
            System.Windows.Application.LoadComponent(app, resourceLocater);
            try
            {
                Window window = null;
                if (conent == "no")
                {
                    window = new WActivation();
                }
                else
                {
                    window = new WLogin();
                    string ShopId = Regedit.GetVal("ShopId");
                    if (ShopId == "0")
                    {
                        string CallPort = Regedit.GetVal("CallPort");
                        try
                        {
                            int.Parse(CallPort);
                        }
                        catch
                        {
                            CallPort = string.Empty;
                        }
                        if (string.IsNullOrEmpty(CallPort) == true || CallPort == "no")
                        {
                            window = new WActivation();
                        }
                    }
                    else
                    {
                    }
                    th_rms2019Bll.SetConnection(PostServer.GetPostServerName(int.Parse(ShopId)));

                }
                app.Run(window);
            }
            catch (Exception ex)
            {
                MessageBox.Show("发生异常program：" + ex.Message + ex.Source);
                //  Environment.Exit(0);
            }
        }
    }
}
