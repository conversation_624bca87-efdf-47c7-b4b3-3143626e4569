using System;
using System.Collections.Generic;
using System.Linq;
using RMSBLL.RMS;
using RMSModel.ExtensionRMS;

namespace RMS2018
{
    /// <summary>
    /// 测试数据加载的简单控制台程序
    /// </summary>
    public class TestDataLoad
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("=== RMS2018 数据加载测试 ===");
            Console.WriteLine();

            try
            {
                // 测试数据库连接和数据加载
                int shopId = 1;
                Console.WriteLine($"测试门店ID: {shopId}");
                Console.WriteLine();

                // 1. 测试房间数据加载
                Console.WriteLine("1. 测试房间数据加载...");
                var roomList = MRmInfoBll.GetMRmInfoByShopid_Rmno(shopId);
                
                if (roomList != null)
                {
                    Console.WriteLine($"   获取到房间总数: {roomList.Count}");
                    
                    // 应用过滤条件（与UShop中相同的逻辑）
                    var filteredRooms = roomList.FindAll(i => i.IsDisplay == false).ToList();
                    Console.WriteLine($"   过滤后房间数量 (IsDisplay == false): {filteredRooms.Count}");
                    
                    if (filteredRooms.Count == 0)
                    {
                        Console.WriteLine("   警告：没有房间通过IsDisplay过滤条件");
                        Console.WriteLine("   显示所有房间作为备用方案");
                        filteredRooms = roomList.ToList();
                    }

                    // 显示房间详情
                    Console.WriteLine("   房间详情:");
                    foreach (var room in filteredRooms.Take(10)) // 只显示前10个
                    {
                        Console.WriteLine($"     房号: {room.RmNo}, 房型: {room.RtNo}, 状态: {room.RmsStatus}, 区域: {room.AreaName}, IsDisplay: {room.IsDisplay}");
                    }
                    if (filteredRooms.Count > 10)
                    {
                        Console.WriteLine($"     ... 还有 {filteredRooms.Count - 10} 个房间");
                    }
                }
                else
                {
                    Console.WriteLine("   错误：无法获取房间数据");
                }

                Console.WriteLine();

                // 2. 测试区域数据生成
                Console.WriteLine("2. 测试区域数据生成...");
                if (roomList != null && roomList.Count > 0)
                {
                    var filteredRooms = roomList.FindAll(i => i.IsDisplay == false).ToList();
                    if (filteredRooms.Count == 0)
                    {
                        filteredRooms = roomList.ToList();
                    }

                    var areas = filteredRooms.GroupBy(r => r.AreaName).Select(r => r.First()).ToList();
                    Console.WriteLine($"   生成的区域数量: {areas.Count}");
                    
                    Console.WriteLine("   区域详情:");
                    foreach (var area in areas)
                    {
                        var roomsInArea = filteredRooms.Where(r => r.AreaName == area.AreaName).ToList();
                        Console.WriteLine($"     区域: {area.AreaName}, 房间数量: {roomsInArea.Count}");
                    }
                }
                else
                {
                    Console.WriteLine("   错误：没有房间数据，无法生成区域");
                }

                Console.WriteLine();

                // 3. 测试门店信息
                Console.WriteLine("3. 测试门店信息...");
                try
                {
                    var shopInfo = MShopInfoBll.GShopInfoAll(shopId);
                    if (shopInfo != null)
                    {
                        Console.WriteLine($"   门店名称: {shopInfo.ShopName}");
                        Console.WriteLine($"   门店地址: {shopInfo.ShopAddress}");
                        Console.WriteLine($"   门店电话: {shopInfo.ShopTel}");
                    }
                    else
                    {
                        Console.WriteLine("   警告：无法获取门店信息");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   错误：获取门店信息失败 - {ex.Message}");
                }

                Console.WriteLine();
                Console.WriteLine("=== 测试完成 ===");
                
                if (roomList != null && roomList.Count > 0)
                {
                    Console.WriteLine("✅ 数据加载正常，应用程序应该能够正确显示房间");
                }
                else
                {
                    Console.WriteLine("❌ 数据加载失败，需要检查数据库连接和数据");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"   堆栈跟踪: {ex.StackTrace}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
