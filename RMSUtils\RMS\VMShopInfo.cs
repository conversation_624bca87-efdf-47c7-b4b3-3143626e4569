﻿using RMSBLL.RMS;
using RMSModel.ExtensionRMS;
using RMSModel.RMS;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace RMSUtils.RMS
{

    public class VMShopInfo
    {

        public VMShopInfo()
        {
            try
            {
                // 优先使用API数据
                if (VMPC.apidata != null && VMPC.apidata.shopList != null)
                {
                    shopList = VMPC.apidata.shopList;
                    workList = VMPC.apidata.workList;
                }
                else
                {
                    // API数据不可用时，直接从数据库获取
                    shopList = MShopInfoBll.GShopInfoAll();
                    workList = MWorkNotShopBll.GetMWorkNotShop(DateTime.Now);
                }
            }
            catch (Exception ex)
            {
                // 如果所有方法都失败，创建默认数据
                System.Diagnostics.Debug.WriteLine($"VMShopInfo初始化失败: {ex.Message}");
                CreateDefaultShopList();
            }

            // 确保shopList不为空
            if (shopList == null)
            {
                CreateDefaultShopList();
            }
        }

        /// <summary>
        /// 创建默认门店列表
        /// </summary>
        private void CreateDefaultShopList()
        {
            shopList = new List<MShopInfo>
            {
                new MShopInfo { ShopId = 0, ShopName = "预约中心" },
                new MShopInfo { ShopId = 1, ShopName = "测试门店1" }
            };
            workList = new List<MWorkNotShop_Shop>();
        }

        public VMShopInfo(bool isapi)
        {
            shopList = MShopInfoBll.GShopInfoAll();
            workList = MWorkNotShopBll.GetMWorkNotShop(DateTime.Now);

        }
        public static List<MShopInfo> shopList { get; set; }
        public List<MWorkNotShop_Shop> workList { get; set; }

        /// <summary>
        /// 获取门店信息
        /// </summary>
        /// <param name="shopid"></param>
        public static MShopInfo GetShopInfo(int shopid)
        {

            MShopInfo mshopinfo = null;
            if (shopid == 0)
            {
                mshopinfo = new MShopInfo() { ShopName = "预约中心" };
            }
            else
            {
                mshopinfo = VMShopInfo.shopList.Find(i => i.ShopId == shopid);
                if (mshopinfo == null)
                    mshopinfo = new MShopInfo() { ShopName = "未知门店:" + shopid };
            }
            return mshopinfo;
        }

        /// <summary>
        /// 门店状态检测
        /// </summary>
        public void SetShopCheck(DateTime datetime)
        {
            if (workList != null && workList.Count > 0)
            {
                foreach (var item in shopList)
                {
                    MWorkNotShop_Shop itemwork = workList.Find(i => i.ShopId == item.ShopId && datetime >= i.WorkTimeStart && datetime <= i.WorkTimeEnd);
                    if (itemwork != null)
                    {
                        item.IsEnabled = false;
                        item.EnabledString = "停";
                    }
                    else
                    {
                        item.IsEnabled = true;
                    }
                }
            }
        }
      

    }
}
