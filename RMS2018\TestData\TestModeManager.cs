using System;
using System.Configuration;
using System.Windows;

namespace RMS2018.TestData
{
    /// <summary>
    /// 测试模式管理器
    /// </summary>
    public static class TestModeManager
    {
        private static bool? _isTestMode = null;

        /// <summary>
        /// 检查是否启用测试模式
        /// </summary>
        public static bool IsTestModeEnabled
        {
            get
            {
                if (_isTestMode == null)
                {
                    _isTestMode = CheckTestModeFromConfig();
                }
                return _isTestMode.Value;
            }
        }

        /// <summary>
        /// 从配置文件检查测试模式
        /// </summary>
        private static bool CheckTestModeFromConfig()
        {
            try
            {
                // 检查App.config中的TestMode设置
                string testModeConfig = ConfigurationManager.AppSettings["TestMode"];
                if (!string.IsNullOrEmpty(testModeConfig) && testModeConfig.ToLower() == "true")
                {
                    return true;
                }

                // 检查数据库连接字符串是否包含本地数据库标识
                string connStr = ConfigurationManager.ConnectionStrings["dbfoodConn"]?.ConnectionString;
                if (!string.IsNullOrEmpty(connStr) && 
                    (connStr.Contains("localhost") || connStr.Contains("127.0.0.1") || connStr.Contains("(local)")))
                {
                    return true;
                }

                // 检查是否为Debug模式
                #if DEBUG
                return true;
                #else
                return false;
                #endif
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 初始化测试模式
        /// </summary>
        public static void InitializeTestMode()
        {
            if (IsTestModeEnabled)
            {
                MockDataService.EnableTestMode();
                
                // 显示测试模式提示
                MessageBox.Show(
                    "系统已启用测试模式！\n\n" +
                    "• 使用模拟房间数据进行测试\n" +
                    "• 转房操作不会影响实际数据库\n" +
                    "• 可以测试免费升级和付费升级功能\n\n" +
                    "测试完成后请切换到生产环境配置。",
                    "测试模式",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
            }
        }

        /// <summary>
        /// 手动启用测试模式
        /// </summary>
        public static void EnableTestMode()
        {
            _isTestMode = true;
            MockDataService.EnableTestMode();
        }

        /// <summary>
        /// 手动禁用测试模式
        /// </summary>
        public static void DisableTestMode()
        {
            _isTestMode = false;
            MockDataService.DisableTestMode();
        }

        /// <summary>
        /// 显示测试模式控制面板
        /// </summary>
        public static void ShowTestModeControlPanel()
        {
            if (!IsTestModeEnabled) return;

            var result = MessageBox.Show(
                "测试模式控制面板\n\n" +
                "当前状态：" + (MockDataService.IsTestMode ? "已启用" : "已禁用") + "\n\n" +
                "点击 '是' 重置测试数据\n" +
                "点击 '否' 切换测试模式状态\n" +
                "点击 '取消' 关闭面板",
                "测试模式控制",
                MessageBoxButton.YesNoCancel,
                MessageBoxImage.Question
            );

            switch (result)
            {
                case MessageBoxResult.Yes:
                    MockDataService.ResetMockData();
                    MessageBox.Show("测试数据已重置！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    break;
                case MessageBoxResult.No:
                    if (MockDataService.IsTestMode)
                    {
                        DisableTestMode();
                        MessageBox.Show("测试模式已禁用！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        EnableTestMode();
                        MessageBox.Show("测试模式已启用！", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    break;
            }
        }
    }
}
