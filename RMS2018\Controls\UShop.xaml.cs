﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Visifire.Charts;
using RMSModel.ExtensionRMS;
using RMSBLL.RMS;
using RMS2018;
using RMSModel.RMS;
using RMSUtils.RMS;
using RMSUtils.Print;
using RMSBLL.DbFood;
using RMSModel.DbFood;
using RMSModel.Config;
using System.ComponentModel;
using System.Reflection;
using RMS2018.Utils.RmLock;
using System.Windows.Threading;

namespace RMS2018.Controls
{


    // Enum.GetName(typeof(MenuItems), MenuItems.清房)
    //取消派房, 微信, 开房, 打印, 打折, 转房, 直落, 客离, 坏房, 慢带, 留房, 清房, 全部清房
    /// <summary>
    /// UShop.xaml 的交互逻辑
    /// </summary>
    public partial class UShop : UserControl
    {
        //


        #region 初始的全局字段信息
        //门店Id
        int shopid;
        List<MRm_Rt_MArea_MShop> list;
        List<MRm_Rt_MArea_MShop> originalList;
        private List<MRm_Rt_MArea_MShop> _cachedOrderedRooms;
        private double? _cachedGridSum;
        private List<MRm_Rt_MArea_MShop> _cachedAreas;

        private Dictionary<int, int> _cachedAreaCounts = new Dictionary<int, int>();
        private Dictionary<string, URadioButton> _roomButtonCache = new Dictionary<string, URadioButton>();
        //状态集合
        List<MBillStatus> MBStatus = new List<MBillStatus>();
        ContextMenu CtMenu;
        Style myStyle;
        /// <summary>
        /// 每个状态的房间数量
        /// </summary>
        public Dictionary<string, MBillStatus> DiStatusCount = new Dictionary<string, MBillStatus>();
        /// <summary>
        /// 所有颜色状态的集合
        /// </summary>
        Dictionary<string, SolidColorBrush> ColorList = new Dictionary<string, SolidColorBrush>();
        /// <summary>
        /// 所有区域开房数量统计
        /// </summary>
        Dictionary<string, MBillStatus> AreaOpenRooms = new Dictionary<string, MBillStatus>();
        /// <summary>
        /// 最大行的索引
        /// </summary>
        public int MaxRowIndex { get; set; }
        /// <summary>
        /// 最大列索引
        /// </summary>
        public int MaxColumnIndex { get; set; }

        /// <summary>
        /// 所有区域
        /// </summary>
        List<MRm_Rt_MArea_MShop> Areas;
        public delegate void CheckedChang(MRm_Rt_MArea_MShop shop);
        /// <summary>
        /// 每个区域的背景颜色,初始颜色为
        /// </summary>
        public string AreaColor = "#f8fcff";

        /// <summary>
        /// 结账、续单、占用、预结数组（用于计算区域数量）
        /// </summary>
        string[] OpenStatus = new string[] { "A", "C", "U", "F" };

        //模式中用到的变量
        /// <summary>
        /// 设置初始行号
        /// </summary>
        int rowcount;
        /// <summary>
        /// 设置初始序号
        /// </summary>
        int count;
        /// <summary>
        /// 设置初始列号
        /// </summary>
        int columncount;
        /// <summary>
        /// 设置区域序号
        /// </summary>
        int i;
        URadioButton[] bt;
        Button[] bt_areas;
        TextBlock[] textlist;
        //

        /// <summary>
        /// 房号选择变更
        /// </summary>
        /// <param name="shop"></param>
        public CheckedChang checkedChang;

        public delegate void MenuItemClick(string MenuItemName, MRm_Rt_MArea_MShop shop);
        public event MenuItemClick menuItemClick;
        //public delegate void UpdModel(Action ac);
        //public UpdModel Updmodel;

        #endregion

        #region 界面设计方法
        /// <summary>
        /// 构造方法
        /// </summary>
        public UShop()
        {
            InitializeComponent();
            try
            {
                // 安全获取shopid，提供多重备用方案
                this.shopid = GetSafeShopId();
                this.MaxRowIndex = 9;
                this.MaxColumnIndex = 19;
                init();
            }
            catch (Exception ex)
            {
                // 记录错误并使用默认值
                System.Diagnostics.Debug.WriteLine($"UShop初始化错误: {ex.Message}");
                this.shopid = 1; // 使用默认shopid
                this.MaxRowIndex = 9;
                this.MaxColumnIndex = 19;
                init();
            }
        }

        /// <summary>
        /// 安全获取ShopId，提供多重备用方案
        /// </summary>
        /// <returns></returns>
        private int GetSafeShopId()
        {
            try
            {
                // 方案1: 尝试从CFixedConfig.shopInfoStatic获取
                if (CFixedConfig.shopInfoStatic != null)
                {
                    return CFixedConfig.shopInfoStatic.ShopId;
                }
            }
            catch { }

            try
            {
                // 方案2: 尝试从SingleRun获取
                var single = SingleRun.GetSingle();
                if (single?.vmpc?.CfixedConfig?.shopInfo != null)
                {
                    return single.vmpc.CfixedConfig.shopInfo.ShopId;
                }
            }
            catch { }

            try
            {
                // 方案3: 从配置文件获取
                string configShopId = System.Configuration.ConfigurationManager.AppSettings["shopid"];
                if (!string.IsNullOrEmpty(configShopId) && int.TryParse(configShopId, out int shopId))
                {
                    return shopId;
                }
            }
            catch { }

            try
            {
                // 方案4: 从注册表获取
                string regShopId = RMSUtils.Regedit.GetVal("ShopId");
                if (!string.IsNullOrEmpty(regShopId) && regShopId != "no" && int.TryParse(regShopId, out int shopId))
                {
                    return shopId;
                }
            }
            catch { }

            // 方案5: 使用默认值
            return 1;
        }

        public Dictionary<string, string> dicCloseTime = new Dictionary<string, string>();


        ///// <summary>
        ///// 传进来参数的构造方法
        ///// </summary>
        ///// <param name="shopid"></param>
        //public UShop(int shopid)
        //{ 
        //    InitializeComponent();
        //    init(shopid);
        //}

        /// <summary>
        /// 初始化方法
        /// </summary>
        /// <param name="shopid"></param>
        private void init()
        {
            try
            {
                //MBillStatus = SingleRun.GetSingle().vmpc.vmBillStatus.billStatusList;
                VMBillStatus vmBstas = new VMBillStatus();
                MBStatus = vmBstas.billStatusList;
                AddContextMenu();

                // 安全获取房间信息
                try
                {
                    //list = MRmInfoBll.GetMRmInfoByShopid_Rmno(shopid);
                    //2021-04-19 jjy 修改,房间是或否展示错误
                    var roomList = MRmInfoBll.GetMRmInfoByShopid_Rmno(shopid);
                    if (roomList != null)
                    {
                        // 修复：显示应该显示的房间（IsDisplay == false 表示显示）
                        // 根据代码逻辑，IsDisplay == false 的房间应该显示
                        list = roomList.FindAll(i => i.IsDisplay == false).ToList();

                        // 调试信息
                        System.Diagnostics.Debug.WriteLine($"获取到房间数量: {roomList.Count}, 过滤后房间数量: {list.Count}");
                        if (list.Count == 0)
                        {
                            System.Diagnostics.Debug.WriteLine("警告：没有房间通过IsDisplay过滤条件，显示所有房间");
                            // 如果没有房间通过过滤，显示所有房间作为备用方案
                            list = roomList.ToList();
                        }
                    }
                    else
                    {
                        list = new List<MRm_Rt_MArea_MShop>();
                    }
                }
                catch (Exception roomEx)
                {
                    System.Diagnostics.Debug.WriteLine($"获取房间信息失败: {roomEx.Message}");
                    list = new List<MRm_Rt_MArea_MShop>();
                }

                List<MRmInfoStatus> rmsroomList = MRmInfoBll.GetRmInfoNotDbFood(shopid);//rms状态 超时改API访问
                //  List<MRmInfoStatus> rmsroomList = RMSBLLApi.RMS.MRmInfoBll.GetRmInfoNotDbFood(shopid);//rms状态
                ///**********增加关房提示

                foreach (var item in list)
                {
                    var data = rmsroomList.Find(i => i.RmNo == item.RmNo);
                    var min = (data.CloseTime - DateTime.Now).TotalMinutes;
                    if (min >= 0 && min <= 10)
                    {
                        dicCloseTime.Add(item.RmNo, "");
                    }
                    item.rminsostatus = data;
                }
                if (dicCloseTime.Count > 0)
                {
                    SetCloseTime(dicCloseTime.Count);
                }

                //遍历所有区域，添加到集合Areas中去
                try
                {
                    if (list != null && list.Count > 0)
                    {
                        Areas = list.GroupBy(r => r.AreaName).Select(r => r.First()).ToList();
                    }
                    else
                    {
                        Areas = new List<MRm_Rt_MArea_MShop>();
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Areas初始化失败: {ex.Message}");
                    Areas = new List<MRm_Rt_MArea_MShop>();
                }
                myStyle = (Style)this.FindResource("pRoom");//TabItemStyle 这个样式是引用的资源文件中的样式名称
                DiStatusCountIni();
                AreaOpenRoomsIni();
                ColorListAdd();
                AddRowCol(10, 20);
                //ComBo_Model.Items.Add("模式一");
                //ComBo_Model.Items.Add("模式二");
                //ComBo_Model.SelectedIndex = 0;
                //UserControl();
                //UserControlTwo(0);
                //UserControlThree();
                RmLock_utils.Auto_lock(list);
            }
            catch { }
        }

        /// <summary>
        /// 设置UI模式(根据房间数量自动匹配界面模式)
        /// </summary>
        public void SetUiModel(bool? skipLayout = null)
        {
            // 保存原始列表的副本
            originalList = list.ToList();
            if (skipLayout != false && skipLayout != null)
            {

                originalList = originalList
                .Where(r => r.Number != null && r.Number != "")  // 排除Number为空的记录
                .ToList();

                if (originalList.Count == 0)
                {
                    return;
                }

                /* list = sortedList;*/
                Areas = originalList.GroupBy(r => r.AreaName).Select(r => r.First()).ToList();
                _cachedAreas = Areas;
                double gridSum = 0;
                /*  foreach (var item in Areas)
                  {
                      // gridSum += Math.Ceiling((list.Count(i => i.AreaId == item.AreaId) + 1.0) / grid.ColumnDefinitions.Count);

                      int listcount = originalList.Count(i => i.AreaId == item.AreaId);
                      double row = Math.Ceiling((listcount + 1.0) / grid.ColumnDefinitions.Count);
                      gridSum += Math.Ceiling((listcount + 1.0 + (row * 1 - 1)) / grid.ColumnDefinitions.Count);
                  }*/
                _cachedGridSum = gridSum;
                UserControlSort();
            }
            else
            {
                double gridSum = 0;
                /* list = list.OrderBy(r => r.AreaId).ToList();
                 Areas = list.GroupBy(r => r.AreaName).Select(r => r.First()).ToList();*/

                // 安全检查Areas是否为空
                if (Areas != null && Areas.Count > 0)
                {
                    foreach (var item in Areas)
                    {
                        // gridSum += Math.Ceiling((list.Count(i => i.AreaId == item.AreaId) + 1.0) / grid.ColumnDefinitions.Count);
                        int listcount = list.Count(i => i.AreaId == item.AreaId);
                        double row = Math.Ceiling((listcount + 1.0) / grid.ColumnDefinitions.Count);
                        gridSum += Math.Ceiling((listcount + 1.0 + (row * 1 - 1)) / grid.ColumnDefinitions.Count);
                    }
                    if (gridSum + Areas.Count <= grid.RowDefinitions.Count)
                        UserControlThree();
                    else if (gridSum > grid.RowDefinitions.Count)
                        UserControl();
                    else
                        UserControlTwo(0);
                }
                else
                {
                    // 如果Areas为空，使用默认布局
                    UserControlTwo(0);
                }
            }
        }
        /// <summary>
        /// 晚档排序20250702-lsf
        /// </summary>
        public void UserControlSort(bool forceFullRefresh = false)
        {
            // 清理旧事件
            foreach (var btn in grid.Children.OfType<URadioButton>())
            {
                btn.Checked -= UShop_Checked;
                btn.MouseRightButtonDown -= URadioButton_MouseRightButtonDown;
            }

            // === 1. 初始化部分 ===
            // 重置状态计数
            foreach (var key in DiStatusCount.Keys.ToList()) DiStatusCount[key].RMcount = 0;
            foreach (var key in AreaOpenRooms.Keys.ToList()) AreaOpenRooms[key].RMcount = 0;
            // 清空UI元素
            Sp_StatusRomCount.Children.Clear();
            grid.Children.Clear();
            columncount = count = i = 0;
            rowcount = -1; // 初始为-1，首次++后变为0
            //添加边框
            AddBorder(grid.RowDefinitions.Count);
            //预分配按钮数组空间
            if (bt == null || bt.Length < originalList.Count * 2)
            {
                bt = new URadioButton[originalList.Count * 2];
            }

            //数据排序（使用缓存）
            if (forceFullRefresh || _cachedOrderedRooms == null || _cachedOrderedRooms.Count != originalList.Count)
            {
                //预计算区域计数，避免多次遍历
                _cachedAreaCounts = originalList
                    .Where(r => !string.IsNullOrEmpty(r.Number))
                    .GroupBy(r => r.AreaId)
                    .ToDictionary(g => g.Key, g => g.Count());

                // === 2. 数据排序（按行序-待类型-房间号）===
                _cachedOrderedRooms = originalList
           .Where(r => !string.IsNullOrEmpty(r.Number))
           .OrderBy(r =>
           {
               var parts = r.Number.Split('-');
               int rowOrder;
               return int.TryParse(parts[0], out rowOrder) ? rowOrder : int.MaxValue;//行序
           })
           .ThenBy(r =>
           {
               var parts = r.Number.Split('-');
               int queueType;
               return int.TryParse(parts[1], out queueType) ? queueType : int.MaxValue;//待类型
           })
           .ThenBy(r =>
           {
               var parts = r.Number.Split('-');
               int roomNo;
               return int.TryParse(parts[2], out roomNo) ? roomNo : int.MaxValue;//房间号
           })
           .ToList();
            }


            // === 3. 动态渲染 ===
            string currentArea = null; // 当前区域

            string currentQueueType = null;// 当前待类型
            int currentRowOrder = int.MinValue;// 当前行序(房间号的第一部分)


            bool isFirstInGroup = true;// 标记是否为组内第一个房间
            foreach (var room in _cachedOrderedRooms)
            {
                var parts = room.Number.Split('-');
                string area = room.AreaName;

                // 解析待类型
                string queueType;
                if (parts.Length > 1)
                {
                    switch (parts[1])
                    {
                        case "01": queueType = "优先待"; break;
                        case "02": queueType = "其次待"; break;
                        case "03": queueType = "最后待"; break;
                        default: queueType = "其他待"; break;
                    }
                }
                else
                {
                    queueType = "其他待";
                }


                int rowOrder = parts.Length > 0 ? int.Parse(parts[0]) : int.MaxValue;

                // 判断分组条件
                bool isNewArea = area != currentArea;
                bool isNewQueueType = queueType != currentQueueType;
                bool isNewRowOrder = rowOrder != currentRowOrder;
                // 处理新区域的情况
                if (isNewArea)
                {
                    rowcount++;
                    columncount = 0;
                    currentArea = area;
                    currentQueueType = queueType;
                    currentRowOrder = rowOrder;
                    isFirstInGroup = true;

                    // 添加区域按钮
                    var areaBtn = new Button { Content = $"【{area}】" };
                    Grid.SetRow(areaBtn, rowcount);
                    Grid.SetColumn(areaBtn, columncount);
                    grid.Children.Add(areaBtn);
                    columncount++;

                    // 添加待类型按钮
                    var queueBtn = new Button { Content = $"【{queueType}】" };
                    Grid.SetRow(queueBtn, rowcount);
                    Grid.SetColumn(queueBtn, columncount);
                    grid.Children.Add(queueBtn);
                    columncount++;
                }
                // 处理新待类型的情况
                else if (isNewQueueType)
                {
                    rowcount++;
                    columncount = 1;  // 从待类型列开始（跳过区域列）
                    currentQueueType = queueType;
                    isFirstInGroup = true;
                    currentRowOrder = rowOrder;
                    //添加待按钮
                    var queueBtn = new Button { Content = queueType, /* 其他属性不变 */ };
                    Grid.SetRow(queueBtn, rowcount);
                    Grid.SetColumn(queueBtn, columncount);
                    grid.Children.Add(queueBtn);
                    columncount++;
                }
                // 同区域同待类型但行序变化时需要换行
                else if (!isNewArea && !isNewQueueType && isNewRowOrder && !isFirstInGroup)
                {
                    rowcount++;
                    columncount = 2; // 跳过区域和待类型列
                    currentRowOrder = rowOrder;
                }
                else if (columncount > MaxColumnIndex)
                {
                    rowcount++;
                    columncount = 2;
                    currentRowOrder = rowOrder;

                }

                // 标记已经不是组内第一个了
                isFirstInGroup = false;

                // 创建房间按钮
                string roomNo = parts.Length > 2 ? "房间" + parts[2] : "房间未知";
                var roomBtn = new URadioButton
                {
                    Content = roomNo,
                    Style = myStyle,
                    DataContext = room,
                    HorizontalContentAlignment = HorizontalAlignment.Center,
                    VerticalContentAlignment = VerticalAlignment.Center,
                    Margin = new Thickness(2)
                };
                roomBtn.Checked += UShop_Checked;
                roomBtn.MouseRightButtonDown += URadioButton_MouseRightButtonDown;
                // 添加按钮到网格
                Grid.SetRow(roomBtn, rowcount);
                Grid.SetColumn(roomBtn, columncount);
                grid.Children.Add(roomBtn);



                // 更新状态计数（保持不变）
                if (DiStatusCount.ContainsKey(room.RmsStatus))
                {
                    DiStatusCount[room.RmsStatus].RMcount++;

                    // 安全访问ColorList字典
                    var billStatus = DiStatusCount[room.RmsStatus];
                    if (ColorList.ContainsKey(billStatus.BackgroundColor))
                    {
                        room.BackgroundColor = ColorList[billStatus.BackgroundColor];
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"警告：ColorList中不存在背景色键: {billStatus.BackgroundColor}");
                    }

                    if (ColorList.ContainsKey(billStatus.FontColor))
                    {
                        room.FontColor = ColorList[billStatus.FontColor];
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"警告：ColorList中不存在字体色键: {billStatus.FontColor}");
                    }

                    if (OpenStatus.Contains(room.RmsStatus))
                    {
                        // 安全访问AreaOpenRooms字典
                        if (AreaOpenRooms.ContainsKey(room.AreaName))
                        {
                            AreaOpenRooms[room.AreaName].RMcount++;
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"警告：AreaOpenRooms中不存在区域键: {room.AreaName}");
                        }

                        if (AreaOpenRooms.ContainsKey("合计"))
                        {
                            AreaOpenRooms["合计"].RMcount++;
                        }
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"警告：DiStatusCount中不存在状态键: {room.RmsStatus}");
                }

                columncount++;
                count++;

            }

            // === 4. 状态统计 ===
            Sp_StatusCount_Add();
            Sp_AreasCountAdd();
        }
        /// <summary>
        /// 给显示房间数量的StackPanel加TextBlock
        /// </summary>
        private void Sp_StatusCount_Add()
        {
            //遍历所有颜色所代表的状态数量
            foreach (var item in DiStatusCount)
            {
                TextBlock TextBlock = new TextBlock();
                TextBlock.Text = item.Value.StatusName + ": ";
                TextBlock.Width = 70;
                TextBlock.Height = 20;
                TextBlock.Margin = new Thickness(2, 0, 1, 1);
                TextBlock.TextAlignment = TextAlignment.Center;
                // 安全设置背景色和前景色
                if (ColorList.ContainsKey(item.Value.BackgroundColor))
                {
                    TextBlock.Background = ColorList[item.Value.BackgroundColor];
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"警告：ColorList中不存在背景色键: {item.Value.BackgroundColor}");
                    TextBlock.Background = Brushes.LightGray; // 默认背景色
                }

                if (ColorList.ContainsKey(item.Value.FontColor))
                {
                    TextBlock.Foreground = ColorList[item.Value.FontColor];
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"警告：ColorList中不存在字体色键: {item.Value.FontColor}");
                    TextBlock.Foreground = Brushes.Black; // 默认字体色
                }

                TextBlock MinTextBlock = new TextBlock();
                MinTextBlock.DataContext = item.Value;
                MinTextBlock.Height = 20;
                MinTextBlock.Margin = new Thickness(-20, 0, 1, 1);
                MinTextBlock.SetBinding(TextBlock.TextProperty, new Binding("RMcount"));

                // 安全设置MinTextBlock的背景色和前景色
                if (ColorList.ContainsKey(item.Value.BackgroundColor))
                {
                    MinTextBlock.Background = ColorList[item.Value.BackgroundColor];
                }
                else
                {
                    MinTextBlock.Background = Brushes.LightGray; // 默认背景色
                }

                if (ColorList.ContainsKey(item.Value.FontColor))
                {
                    MinTextBlock.Foreground = ColorList[item.Value.FontColor];
                }
                else
                {
                    MinTextBlock.Foreground = Brushes.Black; // 默认字体色
                }
                Sp_StatusRomCount.Children.Add(TextBlock);
                Sp_StatusRomCount.Children.Add(MinTextBlock);
            }
        }

        /// <summary>
        /// 添加显示区域开房状态的数量
        /// </summary>
        private void Sp_AreasCountAdd()
        {
            //循环遍历区域的ACU状态数量集合(包括合计数量)
            foreach (var item in AreaOpenRooms)
            {
                TextBlock TextBlock = new TextBlock();
                TextBlock.Text = item.Key + ":";
                TextBlock.Height = 20;
                TextBlock.Margin = new Thickness(8, 0, 0, 0);
                TextBlock.TextAlignment = TextAlignment.Center;

                TextBlock MinTextBlock = new TextBlock();
                MinTextBlock.DataContext = item.Value;
                MinTextBlock.Height = 20;
                MinTextBlock.Margin = new Thickness(1, 0, 1, 1);
                MinTextBlock.SetBinding(TextBlock.TextProperty, new Binding("RMcount"));
                Sp_StatusRomCount.Children.Add(TextBlock);
                Sp_StatusRomCount.Children.Add(MinTextBlock);
            }




        }

        /// <summary>
        /// 给grid添加行列
        /// </summary>
        /// <param name="rows">行数</param>
        /// <param name="cols">列数</param>
        private void AddRowCol(int rows, int cols)
        {
            for (int i = 0; i < rows; i++)
            {
                grid.RowDefinitions.Add(new RowDefinition { });
            }
            for (int j = 0; j < cols; j++)
            {
                grid.ColumnDefinitions.Add(new ColumnDefinition { });
            }
        }

        /// <summary>
        /// 给grid添加边框
        /// </summary>
        private void AddBorder(int rows)
        {
            //var rows = grid.RowDefinitions.Count;
            var columns = grid.ColumnDefinitions.Count;
            //每个格子添加一个Border进去
            for (int i = 0; i < rows; i++)
            {
                for (int j = 0; j < columns; j++)
                {
                    var border = new Border() { BorderBrush = new SolidColorBrush(Colors.Gray), BorderThickness = new Thickness(1) };
                    border.Background = Brushes.Transparent;
                    border.MouseDown += border_MouseDown;
                    Grid.SetRow(border, i);
                    Grid.SetColumn(border, j);
                    grid.Children.Add(border);

                    //TextBlock text = new TextBlock();

                    //Grid.SetRow(text, i);
                    //Grid.SetColumn(text, j);
                    //grid.Children.Add(text);
                }
            }
        }

        void border_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (nowShop != null)
            {
                nowShop.IsChecked = false;
                var single = SingleRun.GetSingle();
                RmLock_utils.LockRoom(single.vmpc.vmuserinfo.muserinfo.UserId, string.Empty);
            }
            // if (checkedChang!=null) checkedChang(null);
        }

        /// <summary>
        /// 切换区域颜色
        /// </summary>
        private void ChangeAreaColor()
        {
            if (AreaColor == "#f8fcff")
            {
                AreaColor = "#fbfee5";
            }
            else { AreaColor = "#f8fcff"; }
        }

        /// <summary>
        /// 给Grid添加内容控件（模式一）
        /// </summary>
        public void UserControl()
        {
            // 1. 先重置所有计数器
            foreach (var key in DiStatusCount.Keys.ToList())
            {
                DiStatusCount[key].RMcount = 0; // 重置每种状态的房间数
            }

            foreach (var key in AreaOpenRooms.Keys.ToList())
            {
                AreaOpenRooms[key].RMcount = 0; // 重置每个区域的开放房间数（包括"合计"）
            }
            Sp_StatusRomCount.Children.Clear();

            grid.Children.Clear();

            rowcount = 0; count = 0; columncount = 0; i = 0;
            AddBorder(grid.RowDefinitions.Count);
            //给初始变量赋值
            //rowcount = 0; count = 0; columncount = 0; i = 0;
            bt_areas = new Button[Areas.Count * 2];
            bt = new URadioButton[list.Count * 2];
            textlist = new TextBlock[list.Count * 2];
            //遍历所有区域  
            foreach (MRm_Rt_MArea_MShop Area in Areas)
            {
                ChangeAreaColor();
                SolidColorBrush color = ColorList[AreaColor];
                if (i != 0)
                {
                    TextBlock tb = new TextBlock();
                    tb.Text = "";
                    grid.Children.Add(tb);
                    tb.SetValue(Grid.RowProperty, rowcount);
                    tb.SetValue(Grid.ColumnProperty, columncount);
                    columncount++;
                    i++;
                    if (columncount > MaxColumnIndex)
                    {
                        rowcount += 1;
                        if (rowcount > MaxRowIndex)
                        {
                            break;
                        }
                        columncount = 0;
                    }
                }

                bt_areas[i] = new Button();
                //  bt_areas[i].IsEnabled = false;
                bt_areas[i].Content = "" + Area.AreaName;
                grid.Children.Add(bt_areas[i]);
                //2021-05-11 jjy 修改天河店房号重新排版，时尚一区、时尚二区、kboss转行
                if (shopid == 3 && (Area.AreaName == "时尚一区" || Area.AreaName == "时尚二区" || Area.AreaName == "kboss"))
                {
                    rowcount = rowcount + 1;
                    columncount = 0;
                }
                bt_areas[i].SetValue(Grid.RowProperty, rowcount);
                bt_areas[i].SetValue(Grid.ColumnProperty, columncount);
                columncount++;
                i++;
                if (columncount > MaxColumnIndex)
                {
                    rowcount += 1;
                    if (rowcount > MaxRowIndex)
                    {
                        break;
                    }
                    columncount = 0;
                }

                //循环遍历此区域的所有房号
                foreach (MRm_Rt_MArea_MShop ms in list)
                {
                    if (ms.AreaName == Area.AreaName)
                    {
                        if (DiStatusCount.Keys.Contains(ms.RmsStatus))
                        {
                            DiStatusCount[ms.RmsStatus].RMcount++;
                            ms.BackgroundColor = ColorList[DiStatusCount[ms.RmsStatus].BackgroundColor];
                            ms.FontColor = ColorList[DiStatusCount[ms.RmsStatus].FontColor];
                            if (OpenStatus.Contains(ms.RmsStatus))
                            {
                                AreaOpenRooms[Area.AreaName].RMcount++;
                                AreaOpenRooms["合计"].RMcount++;
                            }
                        }

                        bt[count] = new URadioButton();
                        bt[count].Checked += UShop_Checked;
                        //Style这行代码耗时过长
                        bt[count].Style = myStyle;
                        bt[count].Background = color;
                        bt[count].DataContext = ms;

                        bt[count].MouseRightButtonDown += URadioButton_MouseRightButtonDown;
                        grid.Children.Add(bt[count]);
                        bt[count].SetValue(Grid.RowProperty, rowcount);
                        bt[count].SetValue(Grid.ColumnProperty, columncount);

                        columncount++;
                        count++;
                        //如果单元格列索引大于最大索引列.就再添加一行，并重置列号
                        if (columncount > MaxColumnIndex)
                        {
                            rowcount += 1;
                            if (rowcount > MaxRowIndex)
                            {
                                break;
                            }
                            columncount = 1;
                        }
                    }
                }
            }
            Sp_StatusCount_Add();
            Sp_AreasCountAdd();
        }

        /// <summary>
        /// 给Grid添加内容控件（模式二）
        /// </summary>
        /// <param name="Area_IntervalNumber">区域之间的间隔行数</param>
        public void UserControlTwo(int Area_IntervalNumber)
        {
            // 安全检查：确保Areas不为null
            if (Areas == null)
            {
                System.Diagnostics.Debug.WriteLine("警告：Areas为null，尝试重新初始化");
                try
                {
                    if (list != null && list.Count > 0)
                    {
                        Areas = list.GroupBy(r => r.AreaName).Select(r => r.First()).ToList();
                    }
                    else
                    {
                        Areas = new List<MRm_Rt_MArea_MShop>();
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"重新初始化Areas失败: {ex.Message}");
                    Areas = new List<MRm_Rt_MArea_MShop>();
                }
            }

            // 1. 先重置所有计数器
            foreach (var key in DiStatusCount.Keys.ToList())
            {
                DiStatusCount[key].RMcount = 0; // 重置每种状态的房间数
            }

            foreach (var key in AreaOpenRooms.Keys.ToList())
            {
                AreaOpenRooms[key].RMcount = 0; // 重置每个区域的开放房间数（包括"合计"）
            }
            Sp_StatusRomCount.Children.Clear();
            grid.Children.Clear();
            rowcount = 0; count = 0; columncount = 0; i = 0;


            AddBorder(grid.RowDefinitions.Count);
            //给初始变量赋值
            //rowcount = 0; count = 0; columncount = 0; i = 0;
            bt = new URadioButton[list.Count * 2];
            //bool判断第一个区域的房间数是否小于最大列，true为是，false为否
            bool Able = false;
            //遍历所有区域
            foreach (MRm_Rt_MArea_MShop Area in Areas)
            {
                ChangeAreaColor();
                SolidColorBrush color = ColorList[AreaColor];

                if (rowcount != 0 || Able == true)
                {
                    rowcount++;
                    if (rowcount > MaxRowIndex)
                    {
                        break;
                    }
                    columncount = 0;
                }
                //如果刚好第一个区域的没有或占满第一行
                if (rowcount == 0 && list.FindAll(L => L.AreaName == Area.AreaName).Count <= MaxColumnIndex)
                {
                    Able = true;
                }
                Button tb = new Button();
                // tb.IsEnabled = false;
                tb.Content = "" + Area.AreaName;
                grid.Children.Add(tb);
                tb.SetValue(Grid.RowProperty, rowcount);
                tb.SetValue(Grid.ColumnProperty, columncount);
                columncount++;
                i++;

                //循环遍历此区域的所有房号
                foreach (MRm_Rt_MArea_MShop ms in list)
                {
                    if (ms.AreaName == Area.AreaName)
                    {
                        if (DiStatusCount.Keys.Contains(ms.RmsStatus))
                        {
                            DiStatusCount[ms.RmsStatus].RMcount++;

                            // 安全访问ColorList字典
                            var billStatus = DiStatusCount[ms.RmsStatus];
                            if (ColorList.ContainsKey(billStatus.BackgroundColor))
                            {
                                ms.BackgroundColor = ColorList[billStatus.BackgroundColor];
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"警告：ColorList中不存在背景色键: {billStatus.BackgroundColor}");
                            }

                            if (ColorList.ContainsKey(billStatus.FontColor))
                            {
                                ms.FontColor = ColorList[billStatus.FontColor];
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine($"警告：ColorList中不存在字体色键: {billStatus.FontColor}");
                            }

                            if (OpenStatus.Contains(ms.RmsStatus))
                            {
                                // 安全访问AreaOpenRooms字典
                                if (AreaOpenRooms.ContainsKey(Area.AreaName))
                                {
                                    AreaOpenRooms[Area.AreaName].RMcount++;
                                }
                                else
                                {
                                    System.Diagnostics.Debug.WriteLine($"警告：AreaOpenRooms中不存在区域键: {Area.AreaName}");
                                }

                                if (AreaOpenRooms.ContainsKey("合计"))
                                {
                                    AreaOpenRooms["合计"].RMcount++;
                                }
                            }
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"警告：DiStatusCount中不存在状态键: {ms.RmsStatus}");
                        }

                        bt[count] = new URadioButton();
                        bt[count].Checked += UShop_Checked;
                        //Style这行代码耗时过长
                        bt[count].Style = myStyle;
                        bt[count].Background = color;
                        bt[count].DataContext = ms;

                        bt[count].MouseRightButtonDown += URadioButton_MouseRightButtonDown;
                        grid.Children.Add(bt[count]);
                        bt[count].SetValue(Grid.RowProperty, rowcount);
                        bt[count].SetValue(Grid.ColumnProperty, columncount);

                        columncount++;
                        count++;
                        //如果单元格列索引大于最大索引列.就再添加一行，并重置列号
                        if (columncount > MaxColumnIndex && ms != list.FindAll(j => j.AreaName == Area.AreaName).Last<MRm_Rt_MArea_MShop>())
                        {
                            rowcount += 1;
                            if (rowcount > MaxRowIndex)
                            {
                                break;
                            }
                            columncount = 1;
                        }
                    }
                }
                rowcount += Area_IntervalNumber;
            }
            Sp_StatusCount_Add();
            Sp_AreasCountAdd();
        }

        /// <summary>
        /// 给Grid添加内容控件（模式三）
        /// </summary>
        public void UserControlThree()
        {
            UserControlTwo(1);
        }
        MRm_Rt_MArea_MShop nowShop = null;//当前选中的房号
        /// <summary>
        /// 选中房号控件时触发变更通知
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        void UShop_Checked(object sender, RoutedEventArgs e)
        {
            Dispatcher.BeginInvoke(new Action(() =>
            {
                try
                {
                    if (checkedChang != null && sender is URadioButton)
                    {
                        URadioButton radio = sender as URadioButton;
                        MRm_Rt_MArea_MShop shop = radio.DataContext as MRm_Rt_MArea_MShop;
                        if (shop != null)
                        {
                            nowShop = shop;
                            checkedChang(shop);
                        }
                    }
                }
                finally
                {
                    CommandManager.InvalidateRequerySuggested(); // 刷新命令状态
                }
            }), DispatcherPriority.Background);
        }

        /// <summary>
        /// 添加菜单选项
        /// </summary>
        /// <returns></returns>
        public void AddContextMenu()
        {
            CtMenu = new ContextMenu();
            Type type = typeof(MenuItems);

            //遍历枚举所有元素
            foreach (FieldInfo fi in type.GetFields(BindingFlags.Public | BindingFlags.Static))
            {
                MenuItems item = (MenuItems)fi.GetValue(null);
                //根据元素获取对应的描述字符串（中文）
                string Value = GetDescription(item);
                MenuItem s = new MenuItem();
                s.Foreground = Brushes.Black;
                s.Header = Value;
                s.Click += MenuItem_Click;
                s.IsEnabled = true;
                CtMenu.Items.Add(s);
            }
        }

        /// <summary>
        /// 点击菜单选中项
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void MenuItem_Click(object sender, RoutedEventArgs e)
        {
            //获取点击的menuitem父级元素的开启控件
            URadioButton URadioButton = ((e.Source as MenuItem).Parent as ContextMenu).PlacementTarget as URadioButton;
            MRm_Rt_MArea_MShop ms = URadioButton.DataContext as MRm_Rt_MArea_MShop;
            MenuItem item = sender as MenuItem;
            //string oldstatus = ms.RmsStatus;
            //if(item.Header.ToString()=="微信"){
            //    ms.RmsStatus = "U";
            //    SetChangeStatus(ms, oldstatus);
            //}
            //else if (item.Header.ToString() == "直落")
            //{
            //    ms.RmsStatus = "E";
            //    SetChangeStatus(ms, oldstatus);
            //}
            if (menuItemClick != null)
            {
                menuItemClick(item.Header.ToString(), ms);
            }

            ////string status=ms.RmsStatus;
            //if (item.Header.ToString() == GetDescription(MenuItems.CancelWeChat))
            //{
            //    //  ms.RmsStatus = "E";
            //}
            //else if (item.Header.ToString() == GetDescription(MenuItems.WeChat))
            //{
            //  //  ms.RmsStatus = "V";
            //}
            //else if (item.Header.ToString() == GetDescription(MenuItems.StayRoom))
            //{

            //}
            //else if (item.Header.ToString() == GetDescription(MenuItems.BadRoom))
            //{

            //}
            //else if (item.Header.ToString() == GetDescription(MenuItems.Slowband))
            //{

            //}
            //else if (item.Header.ToString() == GetDescription(MenuItems.CleanRoom))
            //{

            //}
            //else if (item.Header.ToString() == GetDescription(MenuItems.AllCleanRooms))
            //{

            //}
            //else if (item.Header.ToString() == GetDescription(MenuItems.Discount))
            //{
            //    //   ms.IsDiscount = 1;
            //}
            //else if (item.Header.ToString() == GetDescription(MenuItems.CancelDiscount))
            //{
            //    //  ms.IsDiscount = 0;
            //}
            //else if (item.Header.ToString() == GetDescription(MenuItems.CancelSendRoom))
            //{
            //  //   ms.RmsStatus = "E";
            //}

            //SetChangeStatus(ms, status);
            //SetRoomStatusRefresh();
            // EnableMenuItem(URadioButton);
            //else if (item.Header.ToString() == "客离")
            //{
            //    SetChangeStatus(ms.RmNo, "");
            //}
            //else if (item.Header.ToString() == "直落")
            //{
            //    SetChangeStatus(ms.RmNo, "");
            //}
            //else if (item.Header.ToString() == "转房")
            //{
            //    SetChangeStatus(ms.RmNo, "");
            //}
            //else if (item.Header.ToString() == "打折")
            //{
            //    SetChangeStatus(ms.RmNo, "");
            //}
            //else if (item.Header.ToString() == "打印")
            //{
            //    SetChangeStatus(ms.RmNo, "");
            //}
            //else if (item.Header.ToString() == "开房")
            //{
            //    SetChangeStatus(ms.RmNo, "");
            //}

            // MessageBox.Show(item.Header.ToString() + ms.RmNo);
            //if (menuItemClick != null)
            //{
            //    menuItemClick(item.Header.ToString(), ms);
            //}
        }

        /// <summary>
        /// 屏蔽URadioButton的菜单的一些选项
        /// </summary>
        /// <param name="uc"></param>
        public void EnableMenuItem(URadioButton uc)
        {

            MRm_Rt_MArea_MShop ms = uc.DataContext as MRm_Rt_MArea_MShop;
            foreach (MenuItem mi in CtMenu.Items)
            {

                if (ms.rminsostatus != null && ms.rminsostatus.IsDiscount == 1)
                {
                    if (mi.Header.ToString() == GetDescription(MenuItems.Discount))
                    {
                        mi.Visibility = Visibility.Collapsed;
                        mi.IsEnabled = false;
                    }
                    if (mi.Header.ToString() == GetDescription(MenuItems.CancelDiscount))
                    {
                        mi.Visibility = Visibility.Visible;
                        mi.IsEnabled = true;
                    }
                }
                if (ms.rminsostatus != null && ms.rminsostatus.IsDiscount == 0)
                {
                    if (mi.Header.ToString() == GetDescription(MenuItems.Discount))
                    {
                        mi.Visibility = Visibility.Visible;
                        mi.IsEnabled = true;
                    }
                    if (mi.Header.ToString() == GetDescription(MenuItems.CancelDiscount))
                    {
                        mi.Visibility = Visibility.Collapsed;
                        mi.IsEnabled = false;
                    }
                }
                if (ms.RmsStatus == "V")
                {
                    if (mi.Header.ToString() == GetDescription(MenuItems.WeChat))
                    {
                        mi.Visibility = Visibility.Collapsed;
                        mi.IsEnabled = false;
                    }
                    if (mi.Header.ToString() == GetDescription(MenuItems.CancelWeChat))
                    {
                        mi.Visibility = Visibility.Visible;
                        mi.IsEnabled = true;
                    }
                }
                if (ms.RmsStatus != "V")
                {
                    if (mi.Header.ToString() == GetDescription(MenuItems.WeChat))
                    {
                        mi.Visibility = Visibility.Visible;
                        mi.IsEnabled = true;
                    }
                    if (mi.Header.ToString() == GetDescription(MenuItems.CancelWeChat))
                    {
                        mi.Visibility = Visibility.Collapsed;
                        mi.IsEnabled = false;
                    }
                }
                if (ms.RmsStatus == "W")
                {
                    if (mi.Header.ToString() == GetDescription(MenuItems.CancelSendRoom))
                    {
                        mi.Visibility = Visibility.Visible;
                    }
                }
                if (ms.RmsStatus != "W")
                {
                    if (mi.Header.ToString() == GetDescription(MenuItems.CancelSendRoom))
                    {
                        mi.Visibility = Visibility.Collapsed;
                    }
                }

                if (mi.Header.ToString() == GetDescription(MenuItems.AddTimeOpen))
                {
                    mi.Visibility = Visibility.Collapsed;
                }
                //可用状态
                if (ms.RmsStatus == "E")
                {
                    if (mi.Header.ToString() == GetDescription(MenuItems.AddNewTime) || mi.Header.ToString() == GetDescription(MenuItems.WeChat) || mi.Header.ToString() == GetDescription(MenuItems.BadRoom) || mi.Header.ToString() == GetDescription(MenuItems.Slowband) || mi.Header.ToString() == GetDescription(MenuItems.StayRoom))
                    {
                        mi.IsEnabled = true;
                        mi.Visibility = Visibility.Visible;
                    }
                    else
                    {
                        mi.IsEnabled = false;
                    }
                    if (mi.Header.ToString() == GetDescription(MenuItems.AddTimeOpen))
                    {
                        mi.IsEnabled = true;
                        mi.Visibility = Visibility.Visible;
                    }

                }
                //续单和占用状态和结账状态
                else if (ms.RmsStatus == "C" || ms.RmsStatus == "U" || ms.RmsStatus == "A")
                {
                    if (mi.Header.ToString() == GetDescription(MenuItems.AddNewTime) || mi.Header.ToString() == GetDescription(MenuItems.Discount) || mi.Header.ToString() == GetDescription(MenuItems.Print) || (mi.Header.ToString() == GetDescription(MenuItems.ChangeRoom) && ms.RmsStatus != "A") || mi.Header.ToString() == GetDescription(MenuItems.CancelDiscount))
                    {
                        mi.IsEnabled = true;
                    }
                    else
                    {
                        mi.IsEnabled = false;
                    }
                }
                //清洁状态
                else if (ms.RmsStatus == "D")
                {
                    if (mi.Header.ToString() == GetDescription(MenuItems.CleanRoom) || mi.Header.ToString() == GetDescription(MenuItems.AllCleanRooms))
                    {
                        mi.IsEnabled = true;
                    }
                    else
                    {
                        mi.IsEnabled = false;
                    }
                }

                //派房状态
                else if (ms.RmsStatus == "W")
                {
                    if (mi.Header.ToString() == GetDescription(MenuItems.OpenRoom) || mi.Header.ToString() == GetDescription(MenuItems.Guestleave) || mi.Header.ToString() == GetDescription(MenuItems.ChangeRoom) || mi.Header.ToString() == GetDescription(MenuItems.CancelSendRoom) || mi.Header.ToString() == GetDescription(MenuItems.Print) || mi.Header.ToString() == GetDescription(MenuItems.Discount) || mi.Header.ToString() == GetDescription(MenuItems.CancelDiscount))
                    {
                        mi.IsEnabled = true;
                    }
                    else
                    {
                        mi.IsEnabled = false;
                    }
                }
                //预转状态
                else if (ms.RmsStatus == "H")
                {
                    if (mi.Header.ToString() == GetDescription(MenuItems.ChangeRoom))
                    {
                        mi.IsEnabled = true;
                    }
                    else
                    {
                        mi.IsEnabled = false;
                    }
                }

                //预结状态
                else if (ms.RmsStatus == "F")
                {
                    if (mi.Header.ToString() == GetDescription(MenuItems.Guestleave))
                    {
                        mi.IsEnabled = true;
                    }
                    else
                    {
                        mi.IsEnabled = false;
                    }
                }
                //留房状态
                else if (ms.RmsStatus == "R")
                {
                    if (mi.Header.ToString() == GetDescription(MenuItems.Guestleave))
                    {
                        mi.IsEnabled = true;
                    }
                    else
                    {
                        mi.IsEnabled = false;
                    }
                }

                //慢带状态
                else if (ms.RmsStatus == "L")
                {
                    if (mi.Header.ToString() == GetDescription(MenuItems.Guestleave))
                    {
                        mi.IsEnabled = true;
                    }
                    else
                    {
                        mi.IsEnabled = false;
                    }
                }
                //坏房状态
                else if (ms.RmsStatus == "B")
                {
                    if (mi.Header.ToString() == GetDescription(MenuItems.Guestleave))
                    {
                        mi.IsEnabled = true;
                    }
                    else
                    {
                        mi.IsEnabled = false;
                    }
                }
                if (mi.Header.ToString() == "顾客生日" || mi.Header.ToString() == "取消顾客生日")
                {
                    if (ms.rminsostatus != null && ms.rminsostatus.IsBirthday == true)
                    {
                        mi.Header = "取消顾客生日";

                    }
                    else
                    {
                        mi.Header = "顾客生日";

                    }
                    mi.IsEnabled = true;
                }
            }
        }

        /// <summary>
        /// 房号控件右键效果，切换选中项
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void URadioButton_MouseRightButtonDown(object sender, MouseButtonEventArgs e)
        {
            URadioButton URadioButton = sender as URadioButton;
            URadioButton.IsChecked = true;
            URadioButton.ContextMenu = CtMenu;
            EnableMenuItem(URadioButton);
        }

        #endregion

        #region 用于逻辑处理的方法
        /// <summary>
        /// 初始化每个状态的房间数量的集合
        /// </summary>
        public void DiStatusCountIni()
        {
            try
            {
                // 确保DiStatusCount字典已清空
                DiStatusCount.Clear();

                if (MBStatus != null && MBStatus.Count > 0)
                {
                    for (int i = 0; i < MBStatus.Count; i++)
                    {
                        MBStatus[i].RMcount = 0;
                        if (!DiStatusCount.ContainsKey(MBStatus[i].StatusChar))
                        {
                            DiStatusCount.Add(MBStatus[i].StatusChar, MBStatus[i]);
                        }
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("警告：MBStatus为空，创建默认状态");
                    CreateDefaultBillStatus();
                }

                System.Diagnostics.Debug.WriteLine($"DiStatusCount初始化完成，包含 {DiStatusCount.Count} 个状态");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"DiStatusCount初始化失败: {ex.Message}");
                CreateDefaultBillStatus();
            }
        }

        /// <summary>
        /// 创建默认的账单状态（当数据库为空时使用）
        /// </summary>
        private void CreateDefaultBillStatus()
        {
            try
            {
                DiStatusCount.Clear();

                var defaultStatuses = new[]
                {
                    new { StatusChar = "E", StatusName = "空闲", BackgroundColor = "#90EE90", FontColor = "#000000" },
                    new { StatusChar = "U", StatusName = "占用", BackgroundColor = "#FF6347", FontColor = "#FFFFFF" },
                    new { StatusChar = "C", StatusName = "清扫", BackgroundColor = "#FFD700", FontColor = "#000000" },
                    new { StatusChar = "W", StatusName = "维修", BackgroundColor = "#808080", FontColor = "#FFFFFF" },
                    new { StatusChar = "F", StatusName = "预结", BackgroundColor = "#FFA500", FontColor = "#000000" }
                };

                foreach (var status in defaultStatuses)
                {
                    var billStatus = new MBillStatus
                    {
                        StatusChar = status.StatusChar,
                        StatusName = status.StatusName,
                        BackgroundColor = status.BackgroundColor,
                        FontColor = status.FontColor,
                        RMcount = 0
                    };
                    DiStatusCount.Add(status.StatusChar, billStatus);
                }

                System.Diagnostics.Debug.WriteLine("已创建默认账单状态");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建默认账单状态失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 初始化区域开房数量集合
        /// </summary>
        public void AreaOpenRoomsIni()
        {
            try
            {
                // 确保AreaOpenRooms字典已清空
                AreaOpenRooms.Clear();

                if (list != null && list.Count > 0)
                {
                    List<MRm_Rt_MArea_MShop> Areas = list.GroupBy(r => r.AreaName)
                        .Where(g => !string.IsNullOrEmpty(g.Key))
                        .Select(r => r.First()).ToList();

                    foreach (var Area in Areas)
                    {
                        if (!AreaOpenRooms.ContainsKey(Area.AreaName))
                        {
                            AreaOpenRooms.Add(Area.AreaName, new MBillStatus());
                        }
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("警告：房间列表为空，无法初始化区域统计");
                }

                // 确保"合计"项存在
                if (!AreaOpenRooms.ContainsKey("合计"))
                {
                    AreaOpenRooms.Add("合计", new MBillStatus());
                }

                System.Diagnostics.Debug.WriteLine($"AreaOpenRooms初始化完成，包含 {AreaOpenRooms.Count} 个区域");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"AreaOpenRooms初始化失败: {ex.Message}");
                // 确保至少有"合计"项
                AreaOpenRooms.Clear();
                AreaOpenRooms.Add("合计", new MBillStatus());
            }
        }

        /// <summary>
        /// 锁定房间信息（旧房号,新房号）
        /// </summary>
        public void SetRoomLock(string rmno)
        {
            string[] roomAttr = rmno.Split(',');
            if (roomAttr.Length == 3)
            {
                if (roomAttr[0] == shopid.ToString())
                {
                    MRm_Rt_MArea_MShop shop = list.Find(i => i.RmNo == roomAttr[1]);
                    if (shop != null)
                    {
                        shop.IsEnabled = true;
                    }
                    shop = list.Find(i => i.RmNo == roomAttr[2]);
                    if (shop != null)
                    {
                        shop.IsEnabled = false;
                    }
                }
            }
        }

        /// <summary>
        ///  ColorList添加MBillStatus里面所有的颜色
        /// </summary>
        public void ColorListAdd()
        {
            try
            {
                // 确保ColorList字典已清空
                ColorList.Clear();

                if (MBStatus != null && MBStatus.Count > 0)
                {
                    for (int i = 0; i < MBStatus.Count; i++)
                    {
                        try
                        {
                            if (!string.IsNullOrEmpty(MBStatus[i].BackgroundColor) && !ColorList.Keys.Contains(MBStatus[i].BackgroundColor))
                            {
                                SolidColorBrush Color = new SolidColorBrush((Color)ColorConverter.ConvertFromString(MBStatus[i].BackgroundColor));
                                ColorList.Add(MBStatus[i].BackgroundColor, Color);
                            }
                            if (!string.IsNullOrEmpty(MBStatus[i].FontColor) && !ColorList.Keys.Contains(MBStatus[i].FontColor))
                            {
                                SolidColorBrush Color = new SolidColorBrush((Color)ColorConverter.ConvertFromString(MBStatus[i].FontColor));
                                ColorList.Add(MBStatus[i].FontColor, Color);
                            }
                        }
                        catch (Exception colorEx)
                        {
                            System.Diagnostics.Debug.WriteLine($"颜色转换失败: {colorEx.Message}");
                        }
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("警告：MBStatus为空，创建默认颜色");
                    CreateDefaultColors();
                }

                // 添加区域颜色
                AddAreaColors();

                System.Diagnostics.Debug.WriteLine($"ColorList初始化完成，包含 {ColorList.Count} 种颜色");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"ColorList初始化失败: {ex.Message}");
                CreateDefaultColors();
            }
        }

        /// <summary>
        /// 创建默认颜色（当MBStatus为空时使用）
        /// </summary>
        private void CreateDefaultColors()
        {
            try
            {
                var defaultColors = new[]
                {
                    "#90EE90", "#FF6347", "#FFD700", "#808080", "#FFA500", // 背景色
                    "#000000", "#FFFFFF", "#f8fcff", "#fbfee5" // 字体色和区域色
                };

                foreach (var colorStr in defaultColors)
                {
                    if (!ColorList.ContainsKey(colorStr))
                    {
                        try
                        {
                            SolidColorBrush color = new SolidColorBrush((Color)ColorConverter.ConvertFromString(colorStr));
                            ColorList.Add(colorStr, color);
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"默认颜色转换失败 {colorStr}: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"创建默认颜色失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 添加区域颜色
        /// </summary>
        private void AddAreaColors()
        {
            try
            {
                var areaColors = new[] { "#f8fcff", "#fbfee5" };
                foreach (var colorStr in areaColors)
                {
                    if (!ColorList.ContainsKey(colorStr))
                    {
                        SolidColorBrush color = new SolidColorBrush((Color)ColorConverter.ConvertFromString(colorStr));
                        ColorList.Add(colorStr, color);
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"添加区域颜色失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 根据传进来的房号，状态，更新面板状态,
        /// </summary>
        public void SetChangeStatus(string rmno)
        {
            MRm_Rt_MArea_MShop mroom = list.Find(i => i.RmNo == rmno);
            //SetChangeStatus(mroom, mroom.RmsStatusstatus, billTot, CustTel, IsDiscount);


        }
        /// <summary>
        /// 更新房间状态
        /// </summary>
        /// <param name="mroom">房间对象集合</param>
        private void SetChangeStatus(MRm_Rt_MArea_MShop mroom, string oldStatus)
        {
            if (mroom != null)
            {
                if (OpenStatus.Contains(mroom.RmsStatus) && !OpenStatus.Contains(oldStatus))
                {
                    AreaOpenRooms[mroom.AreaName].RMcount++;
                    AreaOpenRooms["合计"].RMcount++;
                }
                if (OpenStatus.Contains(oldStatus) && !OpenStatus.Contains(mroom.RmsStatus))
                {
                    AreaOpenRooms[mroom.AreaName].RMcount--;
                    AreaOpenRooms["合计"].RMcount--;
                }

                //此处是重新计算区域房间状态的数量
                //AreaOpenRooms.Clear();
                //AreaOpenRoomsIni();
                //foreach (MRm_Rt_MArea_MShop ms in list)
                //{
                //    if (OpenStatus.Contains(ms.RmsStatus))
                //    {
                //        AreaOpenRooms[mroom.AreaName].RMcount++;
                //        AreaOpenRooms["合计"].RMcount++;
                //    }
                //}
                //Sp_AreasCountAdd();

                DiStatusCount[mroom.RmsStatus].RMcount++;
                //ChangeText(item.RmsStatus);
                DiStatusCount[oldStatus].RMcount--;
                mroom.BackgroundColor = ColorList[DiStatusCount[mroom.RmsStatus].BackgroundColor];
                mroom.FontColor = ColorList[DiStatusCount[mroom.RmsStatus].FontColor];
                //ChangeText(item.RmsStatus);
                if (checkedChang != null)
                {
                    checkedChang(null);
                }
            }
        }

        /// <summary>
        /// 改变相应TextBlock的文本的内容
        /// </summary>
        /// <param name="rms"></param>
        public void ChangeText(string rms)
        {
            foreach (var txt in Sp_StatusRomCount.Children.OfType<TextBlock>())
            {
                TextBlock text = txt as TextBlock;
                if (text.Name == rms)
                {
                    text.Text = DiStatusCount[rms].StatusName + ":" + DiStatusCount[rms].RMcount + "间  ";
                }
            }
        }

        /// <summary>
        /// 根据房型获取相关的房号
        /// </summary>
        public List<MRm_Rt_MArea_MShop> GetRtRoomByRtNo(string rtno)
        {
            List<MRm_Rt_MArea_MShop> resultlist = new List<MRm_Rt_MArea_MShop>();
            foreach (var item in list)
            {
                if (item.RtNo == rtno)
                {
                    resultlist.Add(item);
                }
            }
            return resultlist;
        }
        /// <summary>
        /// 获取所有清房房间
        /// </summary>
        public List<MRm_Rt_MArea_MShop> GetRoom_D()
        {
            return list.FindAll(i => i.RmsStatus == "D");
        }
        /// <summary>
        /// 获取指定预约号或电话号码的房间信息
        /// </summary>
        public List<MRm_Rt_MArea_MShop> GetBookNoAndTel(string search)
        {
            return list.FindAll(i => i.rminsostatus != null && (i.rminsostatus.CustTel.Contains(search) || i.rminsostatus.BookNo == search || i.rminsostatus.CustName.Contains(search)));
        }

        #endregion
        /// <summary>
        /// 根据传进来的房号，状态，更新面板状态,
        /// </summary>
        public List<MRm_Rt_MArea_MShop> GetRoomStatus(string status)
        {
            return list.FindAll(i => i.RmsStatus == status);
        }

        //private void Button_Click(object sender, RoutedEventArgs e)
        //{
        //    Dictionary<string, string> list_status = new Dictionary<string, string>();
        //    list_status.Add("101", "W");
        //    ChangeStatus(list_status);
        //}

        /// <summary>
        /// 刷新所有房间状态
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        //private void Refresh_Click(object sender, RoutedEventArgs e)
        //{
        //    Refresh.IsEnabled = false;
        //    //DispatcherHelper.DoEvents(),让按钮灰化得以立刻执行，否则禁用按钮是要在整个事件结束后才会响应
        //    DispatcherHelper.DoEvents();
        //    //System.Threading.Thread.Sleep(1000); 
        //    Refresh();
        //    Refresh.IsEnabled = true;
        //}

        //排除状态
        string NotRmStatus = "WVHLRF";

        /// <summary>
        /// 同步房间状态颜色
        ///    1:查看天王系统跟rms数据是否一致，不一致则执行更新
        ///    2：
        /// </summary>
        public string SetRoomStatusRefresh()
        {
            string erro = string.Empty;
            try
            {
                List<MRmInfoStatus> rmsroomList = MRmInfoBll.GetRmInfoNotDbFood(shopid);//超时取消改访问API
                //      List<MRmInfoStatus> rmsroomList = RMSBLLApi.RMS.MRmInfoBll.GetRmInfoNotDbFood(shopid);//rms状态
                //List<MRmInfoStatus> rmsroomList = MRmInfoBll.GetRmInfoNotDbFood(shopid, changedTime);//超时取消改访问API
                //if (rmsroomList.Count > 0)
                //    changedTime = DateTime.Now;
                //else
                //    return erro;

                List<Room> dbroomList = null;
                try
                {
                    dbroomList = th_rms2019Bll.GetAllRoomInfo();//天王状态
                }
                catch (Exception ex)
                {
                    erro = ex.Message;
                }
                foreach (var mroom in list)
                {
                    string status = mroom.RmsStatus;//拿到本地房间原状态
                    MRmInfoStatus rmsroom = rmsroomList.Find(j => j.RmNo == mroom.RmNo);

                    if (dbroomList != null)
                    {
                        Room dbroom = dbroomList.Find(j => j.RmNo == mroom.RmNo);
                        if (dbroom != null && rmsroom != null)
                        {
                            if (dbroom.RmStatus != rmsroom.DbfoodStatus || dbroom.InvNo != rmsroom.InvNo || (dbroom.RmStatus != rmsroom.RmsStatus && !NotRmStatus.Contains(rmsroom.RmsStatus)))
                            {//如果天王的状态跟服务器的状态、账单总金额、账单号、天王的状态跟服务器的状态并且不等于排除的状态
                                if (!NotRmStatus.Contains(rmsroom.RmsStatus))
                                {
                                    mroom.RmsStatus = dbroom.RmStatus;
                                }
                                else
                                {
                                    mroom.RmsStatus = rmsroom.RmsStatus;
                                }

                                mroom.DbfoodStatus = dbroom.RmStatus;
                                mroom.BillTot = dbroom.Tot;
                                //if (string.IsNullOrEmpty(mroom.RoomToIkey) == false && string.IsNullOrEmpty(dbroom.InvNo) == false)
                                //{
                                //    OpenCacheInfoBll.SetUpdateDataByKey(mroom.RoomToIkey, dbroom.InvNo);
                                //}
                                mroom.InvNo = dbroom.InvNo;
                                if (dbroom.RmStatus == "E")//如果清房后，清空房间的折扣状态
                                {
                                    MRmInfoBll.SetRoomToDbFood_kFee(shopid, mroom.RmNo, mroom.RmsStatus, mroom.InvNo, mroom.DbfoodStatus, mroom.BillTot, string.Empty, 0);
                                    mroom.rminsostatus = new MRmInfoStatus() { RmNo = mroom.RmNo };
                                }
                                else
                                {
                                    if (mroom.RmsStatus == "H")
                                    {
                                        mroom.RmsStatus = dbroom.RmStatus;//防止修改转房失败 
                                    }
                                    MRmInfoBll.SetRoomToDbFood(shopid, mroom.RmNo, mroom.RmsStatus, mroom.InvNo, mroom.DbfoodStatus, mroom.BillTot, 1);

                                }
                                SetChangeStatus(mroom, status);
                                continue;

                            }
                        }
                    }

                    if (mroom.DbfoodStatus != rmsroom.DbfoodStatus || mroom.RmsStatus != rmsroom.RmsStatus || (mroom.rminsostatus != null && mroom.rminsostatus.IsDiscount != rmsroom.IsDiscount) || (mroom.rminsostatus != null && mroom.rminsostatus.RoomToIkey != rmsroom.RoomToIkey) || (mroom.rminsostatus != null && mroom.rminsostatus.IsBirthday != rmsroom.IsBirthday) || (mroom.rminsostatus != null && mroom.rminsostatus.kFee != rmsroom.kFee))
                    {
                        //同步本地实例数据
                        mroom.DbfoodStatus = rmsroom.DbfoodStatus;
                        mroom.RmsStatus = rmsroom.RmsStatus;
                        mroom.RoomToIkey = rmsroom.RoomToIkey;
                        mroom.rminsostatus = rmsroom;
                        SetChangeStatus(mroom, status);
                        //if (string.IsNullOrEmpty(rmsroom.RoomToIkey) == false && string.IsNullOrEmpty(mroom.InvNo) == false)
                        //{
                        //   // OpenCacheInfoBll.SetUpdateDataByKey(mroom.RoomToIkey, mroom.InvNo);
                        //}
                        if (string.IsNullOrEmpty(rmsroom.RoomToIkey) == false && string.IsNullOrEmpty(rmsroom.InvNo) == false && string.IsNullOrEmpty(rmsroom.OrderUserId) == false && string.IsNullOrEmpty(rmsroom.OrderUserName) == false)
                        {
                            try
                            {
                                RMSBLL.DbFood.th_rms2019Bll.SetInsertOrerUserInfo(new Rms2018() { Ikey = rmsroom.RoomToIkey, InvNo = rmsroom.InvNo, OrderName = rmsroom.OrderUserName, OrderUser = rmsroom.OrderUserId });
                            }
                            catch
                            {


                            }
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                erro = ex.Message;
                //  WCall call = new WCall("房间状态同步失败："+ex.Message);

            }
            return erro;
        }
        DateTime changedTime = DateTime.Now;
        /// <summary>
        /// 房间同步并取消天王自动同步
        /// </summary>
        /// <returns></returns> 
        public string SetRoomStatusRefreshNotDbFood()
        {
            string erroString = string.Empty;

            try
            {
                //   List<MRmInfoStatus> rmsroomList = RMSBLLApi.RMS.MRmInfoBll.GetRmInfoNotDbFood(shopid);//rms状态
                List<MRmInfoStatus> rmsroomList = MRmInfoBll.GetRmInfoNotDbFood(shopid, changedTime);//超时取消改访问API
                                                                                                     // List<MRmInfoStatus> rmsroomList = MRmInfoBll.GetRmsRoom();//超时取消改访问API

                if (rmsroomList.Count > 0)
                    changedTime = DateTime.Now;
                else
                    return erroString;

                if (rmsroomList != null)
                {

                    foreach (var mroom in list)
                    {
                        string status = mroom.RmsStatus;//拿到本地房间原状态
                        MRmInfoStatus rmsroom = rmsroomList.Find(j => j.RmNo == mroom.RmNo);

                        if (rmsroom != null)
                        {
                            ////**********判断10分钟内结束的房间
                            var min = (rmsroom.CloseTime - DateTime.Now).TotalMinutes;
                            if (min >= 0 && min <= 10)
                            {
                                if (!dicCloseTime.ContainsKey(rmsroom.RmNo))
                                    dicCloseTime.Add(rmsroom.RmNo, "");

                            }
                            else
                            {
                                if (dicCloseTime.ContainsKey(rmsroom.RmNo))
                                    dicCloseTime.Remove(rmsroom.RmNo);
                            }


                            if (mroom.DbfoodStatus != rmsroom.DbfoodStatus || mroom.RmsStatus != rmsroom.RmsStatus || (mroom.rminsostatus != null && mroom.rminsostatus.IsDiscount != rmsroom.IsDiscount) || (mroom.rminsostatus != null && mroom.rminsostatus.RoomToIkey != rmsroom.RoomToIkey) || (mroom.rminsostatus != null && mroom.rminsostatus.IsBirthday != rmsroom.IsBirthday) || (mroom.rminsostatus != null && mroom.rminsostatus.kFee != rmsroom.kFee))
                            {
                                //同步本地实例数据
                                mroom.DbfoodStatus = rmsroom.DbfoodStatus;
                                mroom.RmsStatus = rmsroom.RmsStatus;
                                mroom.RoomToIkey = rmsroom.RoomToIkey;
                                mroom.rminsostatus = rmsroom;
                                SetChangeStatus(mroom, status);

                                if (string.IsNullOrEmpty(rmsroom.RoomToIkey) == false && string.IsNullOrEmpty(rmsroom.InvNo) == false && string.IsNullOrEmpty(rmsroom.OrderUserId) == false && string.IsNullOrEmpty(rmsroom.OrderUserName) == false)
                                {
                                    try
                                    {
                                        RMSBLL.DbFood.th_rms2019Bll.SetInsertOrerUserInfo(new Rms2018() { Ikey = rmsroom.RoomToIkey, InvNo = rmsroom.InvNo, OrderName = rmsroom.OrderUserName, OrderUser = rmsroom.OrderUserId });
                                    }
                                    catch
                                    {


                                    }
                                }

                            }

                        }
                        else
                        {

                            continue;
                            //   erroString += "找不到记录：" + mroom.RmNo;
                        }
                    }

                    if (dicCloseTime.Count > 0)
                    {
                        SetCloseTime(dicCloseTime.Count);
                    }


                }
                else
                {
                    erroString = "rms记录获取失败!";
                }
            }
            catch (Exception ex)
            {

                erroString = "异常:" + ex.Message;

            }
            return erroString;

        }


        public void SetCloseTime(int CloseRmCou)
        {
            this.Dispatcher.Invoke(new Action(() =>
            {
                tbCloseTime.Text = "10分钟内关房总数:" + CloseRmCou;
            }));
        }
        ///// <summary>
        ///// 同步房间状态颜色
        /////    1:查看天王系统跟rms数据是否一致，不一致则执行更新
        /////    2：
        ///// </summary>
        //public string SetRoomStatusRefresh_old()
        //{
        //    try
        //    {
        //        List<MRmInfoStatus> rmsroomList = MRmInfoBll.GetRmInfoNotDbFood(shopid);//rms状态
        //        List<Room> dbroomList = th_rms2019Bll.GetAllRoomInfo();//天王状态
        //        foreach (var mroom in list)
        //        {
        //            string status = mroom.RmsStatus;
        //            Room dbroom = dbroomList.Find(j => j.RmNo == mroom.RmNo);
        //            MRmInfoStatus rmsroom = rmsroomList.Find(j => j.RmNo == mroom.RmNo);
        //            if (dbroom != null && rmsroom != null)
        //            {
        //                if (dbroom.RmStatus != rmsroom.DbfoodStatus || dbroom.Tot != rmsroom.BillTot || dbroom.InvNo != rmsroom.InvNo || (dbroom.RmStatus != rmsroom.RmsStatus && !NotRmStatus.Contains(rmsroom.RmsStatus)))
        //                {//天王同步rms房间信息表
        //                    if (!NotRmStatus.Contains(rmsroom.RmsStatus))
        //                    {
        //                        mroom.RmsStatus = dbroom.RmStatus;
        //                    }
        //                    else
        //                    {
        //                        mroom.RmsStatus = rmsroom.RmsStatus;
        //                    }
        //                    mroom.DbfoodStatus = dbroom.RmStatus;
        //                    mroom.BillTot = dbroom.Tot;
        //                    mroom.InvNo = dbroom.InvNo;
        //                    mroom.BookNo = rmsroom.BookNo;
        //                    mroom.IsDiscount = rmsroom.IsDiscount;
        //                    if (dbroom.RmStatus == "E")//如果清房后，清空房间的折扣状态
        //                        mroom.IsDiscount = 0;
        //                    MRmInfoBll.SetRoomToDbFood(shopid, mroom.RmNo, mroom.RmsStatus, mroom.InvNo, mroom.DbfoodStatus, mroom.BillTot, mroom.IsDiscount);
        //                    SetChangeStatus(mroom, status);
        //                    continue;

        //                }
        //                mroom.BillTot = rmsroom.BillTot;
        //                mroom.InvNo = rmsroom.InvNo;
        //                mroom.BookNo = rmsroom.BookNo;
        //                mroom.CustTel = rmsroom.CustTel;
        //                mroom.IsDiscount = rmsroom.IsDiscount;
        //                if (mroom.DbfoodStatus != rmsroom.DbfoodStatus || mroom.RmsStatus != rmsroom.RmsStatus)
        //                {
        //                    //同步本地实例数据

        //                    mroom.DbfoodStatus = rmsroom.DbfoodStatus;
        //                    mroom.RmsStatus = rmsroom.RmsStatus;
        //                    SetChangeStatus(mroom, status);
        //                }
        //            }

        //        }

        //    }
        //    catch (Exception ex)
        //    {
        //        return ex.Message;
        //        //  WCall call = new WCall("房间状态同步失败："+ex.Message);

        //    }
        //    return "";
        //}

        /// <summary>
        /// 刷新
        /// </summary>
        public void Refresh()
        {
            //ComBo_Model.IsEnabled = false;
            DispatcherHelper.DoEvents();
            grid.Children.Clear();
            Sp_StatusRomCount.Children.Clear();
            DiStatusCount.Clear();
            AreaOpenRooms.Clear();
            DiStatusCountIni();
            AreaOpenRoomsIni();
            //ComBo_Model.IsEnabled = true;
        }

        /// <summary>
        /// 下拉框切换状态
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        //private void ComBo_Model_SelectionChanged(object sender, SelectionChangedEventArgs e)
        //{
        //    if (ComBo_Model.SelectedItem != null)
        //    {
        //        if (ComBo_Model.SelectedItem.ToString() == "模式一")
        //        {
        //                LoadingWait.Visibility = Visibility.Visible;
        //                Refresh();
        //                UserControl();
        //                LoadingWait.Visibility = Visibility.Collapsed;
        //        }
        //        if (ComBo_Model.SelectedItem.ToString() == "模式二")
        //        {
        //            this.Dispatcher.BeginInvoke(System.Windows.Threading.DispatcherPriority.Background, new Action(() =>
        //            {
        //                LoadingWait.Visibility = Visibility.Visible;
        //                Refresh();
        //                UserControlTwo();
        //                LoadingWait.Visibility = Visibility.Collapsed;
        //            }));
        //        }
        //    }
        //}



        ///清除锁定内容
        public void SetClearLock()
        {
            if (list != null)
            {
                foreach (var item in list)
                {
                    if (item.IsEnabled == false)
                        item.IsEnabled = true;
                }
            }
        }


        /// <summary>
        /// 读取枚举中文描述字符串
        /// </summary>
        /// <param name="value"></param>
        /// <returns></returns>
        public static string GetDescription(Enum value)
        {
            FieldInfo fi = value.GetType().GetField(value.ToString());
            DescriptionAttribute[] attributes =
                  (DescriptionAttribute[])fi.GetCustomAttributes(
                  typeof(DescriptionAttribute), false);
            return (attributes.Length > 0) ? attributes[0].Description : value.ToString();
        }

        private void tbCloseTime_MouseDown(object sender, MouseButtonEventArgs e)
        {
            WRoomManage_New wroom = new WRoomManage_New();
            wroom.ShowDialog();
        }
    }
    public enum MenuItems
    {
        [Description("取消派房")]
        CancelSendRoom,
        [Description("微信")]
        WeChat,
        [Description("取消微信")]
        CancelWeChat,
        [Description("开房")]
        OpenRoom,
        [Description("打印")]
        Print,
        [Description("打折")]
        Discount,
        [Description("取消打折")]
        CancelDiscount,
        [Description("转房")]
        ChangeRoom,
        [Description("直落")]
        AddNewTime,
        [Description("客离")]
        Guestleave,
        [Description("坏房")]
        BadRoom,
        [Description("慢带")]
        Slowband,
        [Description("留房")]
        StayRoom,
        [Description("清房")]
        CleanRoom,
        [Description("全部清房")]
        AllCleanRooms,
        [Description("加钟重开")]
        AddTimeOpen,
        [Description("顾客生日")]
        Birthday
    }
}
