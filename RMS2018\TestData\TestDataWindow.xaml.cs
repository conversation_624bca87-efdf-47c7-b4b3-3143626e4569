using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Controls;
using RMSModel.RMS;
using RMSBLL.RMS;
using System.Configuration;
using System.Data.SqlClient;

namespace RMS2018.TestData
{
    /// <summary>
    /// TestDataWindow.xaml 的交互逻辑
    /// </summary>
    public partial class TestDataWindow : Window
    {
        public TestDataWindow()
        {
            InitializeComponent();
            InitializeWindow();
        }

        private void InitializeWindow()
        {
            UpdateStatus();
            LoadRoomData();
        }

        private void UpdateStatus()
        {
            bool isTestMode = TestModeManager.IsTestModeEnabled;
            TestModeStatus.Text = $"测试模式：{(isTestMode ? "已启用" : "未启用")}";
            
            if (MockDataService.IsTestMode)
            {
                StatusText.Text = "状态：使用模拟数据";
                DatabaseStatus.Text = "数据库连接：使用模拟数据（无需数据库）";
            }
            else
            {
                StatusText.Text = "状态：使用真实数据库";
                DatabaseStatus.Text = "数据库连接：连接到真实数据库";
            }
        }

        private void LoadRoomData()
        {
            try
            {
                int shopId = 1;
                var roomData = TestModeRoomService.GetMRmInfo(shopId);
                RoomDataGrid.ItemsSource = roomData;
                
                // 加载房间到下拉框
                var occupiedRooms = roomData.Where(r => r.RmsStatus == "U" || r.RmsStatus == "F").ToList();
                var emptyRooms = roomData.Where(r => r.RmsStatus == "E").ToList();
                
                FromRoomCombo.ItemsSource = occupiedRooms;
                FromRoomCombo.DisplayMemberPath = "RmNo";
                FromRoomCombo.SelectedValuePath = "RmNo";
                
                ToRoomCombo.ItemsSource = emptyRooms;
                ToRoomCombo.DisplayMemberPath = "RmNo";
                ToRoomCombo.SelectedValuePath = "RmNo";
                
                LogMessage($"已加载 {roomData.Count} 个房间数据");
            }
            catch (Exception ex)
            {
                LogMessage($"加载房间数据失败: {ex.Message}");
            }
        }

        private void EnableTestMode_Click(object sender, RoutedEventArgs e)
        {
            TestModeManager.EnableTestMode();
            UpdateStatus();
            LoadRoomData();
            LogMessage("测试模式已启用");
        }

        private void DisableTestMode_Click(object sender, RoutedEventArgs e)
        {
            TestModeManager.DisableTestMode();
            UpdateStatus();
            LoadRoomData();
            LogMessage("测试模式已禁用");
        }

        private void ResetTestData_Click(object sender, RoutedEventArgs e)
        {
            MockDataService.ResetMockData();
            LoadRoomData();
            LogMessage("测试数据已重置");
        }

        private void TestConnection_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string connectionString = ConnectionStringText.Text;
                using (var connection = new SqlConnection(connectionString))
                {
                    connection.Open();
                    DatabaseStatus.Text = "数据库连接：连接成功";
                    LogMessage("数据库连接测试成功");
                }
            }
            catch (Exception ex)
            {
                DatabaseStatus.Text = "数据库连接：连接失败";
                LogMessage($"数据库连接测试失败: {ex.Message}");
            }
        }

        private void OpenRoomChange_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 这里应该打开转房界面
                // 由于我们在测试工具中，可能需要创建一个新的转房窗口实例
                LogMessage("打开转房界面功能需要在主应用程序中实现");
                MessageBox.Show("请在主应用程序中打开转房界面进行测试", "提示", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                LogMessage($"打开转房界面失败: {ex.Message}");
            }
        }

        private void GenerateTestData_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                string scriptPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "TestData", "Create_Test_Data.sql");
                if (System.IO.File.Exists(scriptPath))
                {
                    System.Diagnostics.Process.Start("notepad.exe", scriptPath);
                    LogMessage("已打开测试数据SQL脚本");
                }
                else
                {
                    LogMessage("测试数据SQL脚本文件不存在");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"打开SQL脚本失败: {ex.Message}");
            }
        }

        private void RefreshData_Click(object sender, RoutedEventArgs e)
        {
            LoadRoomData();
            LogMessage("数据已刷新");
        }

        private void TestScenario1_Click(object sender, RoutedEventArgs e)
        {
            // 场景1：免费升级（标准→豪华）
            ExecuteTestScenario("1012", "1021", true, "免费升级测试：标准大床房 → 豪华大床房");
        }

        private void TestScenario2_Click(object sender, RoutedEventArgs e)
        {
            // 场景2：付费升级（标准→总统）
            ExecuteTestScenario("1012", "2031", false, "付费升级测试：标准大床房 → 总统套房");
        }

        private void TestScenario3_Click(object sender, RoutedEventArgs e)
        {
            // 场景3：同级转房（豪华→豪华）
            ExecuteTestScenario("1022", "1023", true, "同级转房测试：豪华大床房 → 豪华大床房");
        }

        private void CustomTest_Click(object sender, RoutedEventArgs e)
        {
            if (FromRoomCombo.SelectedValue == null || ToRoomCombo.SelectedValue == null)
            {
                MessageBox.Show("请选择转出房间和转入房间", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            string fromRoom = FromRoomCombo.SelectedValue.ToString();
            string toRoom = ToRoomCombo.SelectedValue.ToString();
            bool keepRate = KeepRateRadio.IsChecked == true;
            string scenario = $"自定义测试：{fromRoom} → {toRoom} ({(keepRate ? "保持原房型" : "按新房型计费")})";

            ExecuteTestScenario(fromRoom, toRoom, keepRate, scenario);
        }

        private void ExecuteTestScenario(string fromRoom, string toRoom, bool keepRate, string scenarioName)
        {
            try
            {
                LogMessage($"开始执行：{scenarioName}");
                
                if (MockDataService.IsTestMode)
                {
                    // 使用模拟数据
                    MockDataService.MockRoomTransfer(1, fromRoom, toRoom, "测试用户", keepRate);
                    LogMessage($"模拟转房操作完成：{fromRoom} → {toRoom}");
                }
                else
                {
                    // 使用真实数据库
                    if (keepRate)
                    {
                        MRmInfoBll.GetMRmInfo_Up_KeepRate(1, toRoom, fromRoom, "测试用户");
                    }
                    else
                    {
                        MRmInfoBll.GetMRmInfo_Up(1, toRoom, fromRoom, "测试用户");
                    }
                    LogMessage($"数据库转房操作完成：{fromRoom} → {toRoom}");
                }
                
                // 刷新数据显示
                LoadRoomData();
                LogMessage($"测试场景执行成功：{scenarioName}");
            }
            catch (Exception ex)
            {
                LogMessage($"测试场景执行失败：{scenarioName} - {ex.Message}");
            }
        }

        private void LogMessage(string message)
        {
            string timestamp = DateTime.Now.ToString("HH:mm:ss");
            LogOutput.Text += $"[{timestamp}] {message}\n";
            
            // 自动滚动到底部
            if (LogOutput.Parent is ScrollViewer scrollViewer)
            {
                scrollViewer.ScrollToEnd();
            }
        }
    }
}
