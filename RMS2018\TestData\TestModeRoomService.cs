using System;
using System.Collections.Generic;
using System.Linq;
using RMSModel.RMS;
using RMSBLL.RMS;

namespace RMS2018.TestData
{
    /// <summary>
    /// 测试模式房间服务包装器
    /// </summary>
    public static class TestModeRoomService
    {
        /// <summary>
        /// 获取房间信息（支持测试模式）
        /// </summary>
        public static List<MRm_Rt_MArea_MShop> GetMRmInfo(int shopId)
        {
            if (TestModeManager.IsTestModeEnabled && MockDataService.IsTestMode)
            {
                return MockDataService.GetMockRoomData(shopId);
            }
            else
            {
                try
                {
                    return MRmInfoBll.GetMRmInfo(shopId);
                }
                catch (Exception ex)
                {
                    // 如果数据库连接失败，自动启用测试模式
                    if (TestModeManager.IsTestModeEnabled)
                    {
                        System.Windows.MessageBox.Show(
                            $"数据库连接失败，自动启用测试模式：\n{ex.Message}",
                            "数据库连接错误",
                            System.Windows.MessageBoxButton.OK,
                            System.Windows.MessageBoxImage.Warning
                        );
                        
                        MockDataService.EnableTestMode();
                        return MockDataService.GetMockRoomData(shopId);
                    }
                    throw;
                }
            }
        }

        /// <summary>
        /// 执行房间转移（支持测试模式）
        /// </summary>
        public static int GetMRmInfo_Up(int shopid, string RoomToNo, string RoomFromNo, string UserName)
        {
            if (TestModeManager.IsTestModeEnabled && MockDataService.IsTestMode)
            {
                MockDataService.MockRoomTransfer(shopid, RoomFromNo, RoomToNo, UserName, false);
                return 1; // 模拟成功
            }
            else
            {
                return MRmInfoBll.GetMRmInfo_Up(shopid, RoomToNo, RoomFromNo, UserName);
            }
        }

        /// <summary>
        /// 执行房间转移（保持原房型，支持测试模式）
        /// </summary>
        public static int GetMRmInfo_Up_KeepRate(int shopid, string RoomToNo, string RoomFromNo, string UserName)
        {
            if (TestModeManager.IsTestModeEnabled && MockDataService.IsTestMode)
            {
                MockDataService.MockRoomTransfer(shopid, RoomFromNo, RoomToNo, UserName, true);
                return 1; // 模拟成功
            }
            else
            {
                return MRmInfoBll.GetMRmInfo_Up_KeepRate(shopid, RoomToNo, RoomFromNo, UserName);
            }
        }

        /// <summary>
        /// 获取转房记录（支持测试模式）
        /// </summary>
        public static List<RmExchange> GetRmExchangeData(int shopId)
        {
            if (TestModeManager.IsTestModeEnabled && MockDataService.IsTestMode)
            {
                return MockDataService.GetMockRmExchangeData(shopId);
            }
            else
            {
                try
                {
                    return RmExchangeBll.GetData1(shopId);
                }
                catch (Exception ex)
                {
                    // 如果数据库连接失败，返回空列表
                    if (TestModeManager.IsTestModeEnabled)
                    {
                        return new List<RmExchange>();
                    }
                    throw;
                }
            }
        }

        /// <summary>
        /// 获取开房缓存信息（支持测试模式）
        /// </summary>
        public static List<MOpenCacheInfo> GetOpenCacheInfo(int shopId)
        {
            if (TestModeManager.IsTestModeEnabled && MockDataService.IsTestMode)
            {
                return MockDataService.GetMockOpenCacheData(shopId);
            }
            else
            {
                try
                {
                    return OpenCacheInfoBll.GetData(shopId);
                }
                catch (Exception ex)
                {
                    // 如果数据库连接失败，返回空列表
                    if (TestModeManager.IsTestModeEnabled)
                    {
                        return new List<MOpenCacheInfo>();
                    }
                    throw;
                }
            }
        }
    }
}
