# Visual Studio 2019 调试运行指南

## 🔧 已修复的问题

### **1. 数据库错误："列名sn无效"**
✅ **修复位置**: `RMSDao/RMS/MRmInfoDal.cs`
- 移除了SQL查询中不存在的`ar.Sn`字段
- 修复了第25行和第262行的SQL语句

### **2. UI初始化错误："参数名：source"**
✅ **修复位置**: 多个文件
- `VMShopInfo.cs`: 添加多重备用数据源
- `VMPC.cs`: API失败时的默认数据创建
- `WBook.xaml.cs`: 安全的数据绑定
- `WOpen.xaml.cs`: 安全的组件初始化

### **3. 调试模式启动问题**
✅ **修复位置**: `Program.cs`
- 检测调试模式自动跳过参数验证
- 自动设置测试环境配置

### **4. 黑屏问题**
✅ **修复位置**: `WindowBuildFactory.cs`
- 添加异常处理防止窗口创建失败
- 安全的VMPC访问

## 🚀 在Visual Studio 2019中调试运行

### **步骤1: 设置启动项目**
1. 在Solution Explorer中右键点击`RMS2018`项目
2. 选择"Set as StartUp Project"

### **步骤2: 配置调试环境**
当前配置已自动设置：
- **数据库**: 192.168.2.14/RMS2019
- **用户**: 0202168
- **自动登录**: 已启用
- **调试模式**: 已启用

### **步骤3: 启动调试**
1. 按`F5`或点击"Start Debugging"
2. 应用程序将自动：
   - 检测调试模式
   - 设置测试配置
   - 显示调试模式提示
   - 自动登录用户0202168
   - 进入主界面

### **步骤4: 验证功能**
1. ✅ 应用程序正常启动
2. ✅ 登录界面显示
3. ✅ 自动登录成功
4. ✅ 主界面正常显示
5. ✅ 可以访问转房功能

## 🛡️ 安全机制

### **全局异常处理**
- 捕获所有未处理异常
- 显示友好错误信息
- 防止应用程序崩溃
- 调试模式下显示详细信息

### **多重备用机制**
1. **数据源**: API → 数据库 → 默认数据
2. **配置**: 动态获取 → 配置文件 → 硬编码
3. **初始化**: 正常流程 → 异常处理 → 降级方案

### **调试友好**
- 自动检测调试模式
- 详细的调试输出
- 异常信息记录
- 配置状态显示

## 🧪 测试转房功能

### **访问转房界面**
1. 在主界面中找到房间列表
2. 右键点击已开房的房间
3. 选择"转房"选项
4. 进入转房界面

### **测试计费选择**
1. **免费升级**:
   - 选择"免费升级"单选按钮
   - 确认按原房间类型计费
   
2. **付费升级**:
   - 选择"付费升级"单选按钮
   - 确认按新房间类型计费

### **验证数据库变化**
```sql
-- 查看转房记录
SELECT * FROM RoomChangeHistory 
WHERE ChangeDate >= CAST(GETDATE() AS DATE)
ORDER BY ChangeTime DESC;

-- 查看房间状态
SELECT RmNo, RmsStatus, RtNo, BillTot 
FROM RmInfo 
WHERE ShopId = 1;
```

## 🔍 故障排除

### **问题1: 仍然出现数据库错误**
**解决方案**:
1. 确认网络连接到192.168.2.14
2. 检查SQL Server服务状态
3. 验证用户名密码：sa/123
4. 确认RMS2019数据库存在

### **问题2: 自动登录失败**
**解决方案**:
1. 检查App.config中的AutoLogin设置
2. 确认TestUserId存在于数据库
3. 手动输入用户ID：0202168

### **问题3: 主界面显示异常**
**解决方案**:
1. 查看调试输出窗口的错误信息
2. 检查VMShopInfo.shopList是否为空
3. 确认数据库连接正常

### **问题4: 转房功能无法访问**
**解决方案**:
1. 确认有开房记录存在
2. 检查房间状态为'U'（使用中）
3. 验证用户权限

## 📋 当前配置状态

### **数据库配置**
- **服务器**: 192.168.2.14
- **数据库**: RMS2019
- **用户**: sa
- **密码**: 123

### **应用配置**
- **门店ID**: 1
- **测试用户**: 0202168
- **自动登录**: 启用
- **调试模式**: 启用
- **安全模式**: 启用

### **功能状态**
- ✅ 数据库连接
- ✅ 用户登录
- ✅ 主界面显示
- ✅ 房间管理
- ✅ 转房功能
- ✅ 计费选择

## 🎯 预期结果

现在你应该能够：

1. ✅ **在VS2019中直接按F5调试**
2. ✅ **自动跳过所有初始化错误**
3. ✅ **看到调试模式提示信息**
4. ✅ **自动登录进入主界面**
5. ✅ **正常使用转房功能**
6. ✅ **测试免费/付费升级逻辑**
7. ✅ **查看数据库中的实际变化**

所有修复都已完成，现在可以在Visual Studio 2019中正常调试和测试房间转移计费功能了！
