-- =============================================
-- 创建本地测试数据脚本
-- 用于在本地环境中测试房间转移计费功能
-- 执行前请确保已创建相应的数据库和表结构
-- =============================================

-- 使用数据库
USE rms2019;
GO

-- 1. 创建测试门店信息
IF NOT EXISTS (SELECT 1 FROM ShopInfo WHERE ShopId = 1)
BEGIN
    INSERT INTO ShopInfo (ShopId, ShopName, ShopAddress, ShopTel, IsDisplay)
    VALUES (1, '测试门店', '测试地址', '020-12345678', 1);
END

-- 2. 创建测试区域信息
IF NOT EXISTS (SELECT 1 FROM AreaInfo WHERE AreaId = 1 AND ShopId = 1)
BEGIN
    INSERT INTO AreaInfo (AreaId, AreaName, ShopId, Sn, IsDisplay)
    VALUES 
    (1, '1楼', 1, 1, 1),
    (2, '2楼', 1, 2, 1),
    (3, '3楼', 1, 3, 1);
END

-- 3. 创建测试房型信息
IF NOT EXISTS (SELECT 1 FROM RtInfo WHERE RtNo = '001' AND ShopId = 1)
BEGIN
    INSERT INTO RtInfo (IRtKey, ShopId, RtNo, RtName, NumberMin, NumberMax, SortNumber, IsWechatBook)
    VALUES 
    (NEWID(), 1, '001', '标准大床房', 1, 2, 1, 1),
    (NEWID(), 1, '002', '豪华大床房', 1, 2, 2, 1),
    (NEWID(), 1, '003', '总统套房', 1, 4, 3, 1),
    (NEWID(), 1, '004', '商务套房', 1, 3, 4, 1);
END

-- 4. 创建测试房间信息
IF NOT EXISTS (SELECT 1 FROM RmInfo WHERE ShopId = 1)
BEGIN
    -- 1楼 - 标准大床房
    INSERT INTO RmInfo (ShopId, RmNo, RtNo, AreaId, IsExistWc, MealAreaSort, FloorNumber, IsDisplay, RmsStatus, InvNo, RtUp, RoomToIkey, BookNo, CustTel, IsDiscount)
    VALUES 
    (1, '1011', '001', 1, 1, 1, 1, 1, 'E', '', '', '', '', '', 0),
    (1, '1012', '001', 1, 1, 1, 1, 1, 'U', 'INV20250715001', '001', NEWID(), 'BK20250715001', '13800000001', 0),
    (1, '1013', '001', 1, 1, 1, 1, 1, 'C', '', '', '', '', '', 0),
    (1, '1014', '001', 1, 1, 1, 1, 1, 'E', '', '', '', '', '', 0),
    
    -- 1楼 - 豪华大床房
    (1, '1021', '002', 1, 1, 1, 1, 1, 'E', '', '', '', '', '', 0),
    (1, '1022', '002', 1, 1, 1, 1, 1, 'U', 'INV20250715002', '002', NEWID(), 'BK20250715002', '13800000002', 0),
    (1, '1023', '002', 1, 1, 1, 1, 1, 'E', '', '', '', '', '', 0),
    (1, '1024', '002', 1, 1, 1, 1, 1, 'W', '', '', '', '', '', 0),
    
    -- 2楼 - 总统套房
    (1, '2031', '003', 2, 1, 1, 2, 1, 'E', '', '', '', '', '', 0),
    (1, '2032', '003', 2, 1, 1, 2, 1, 'E', '', '', '', '', '', 0),
    (1, '2033', '003', 2, 1, 1, 2, 1, 'U', 'INV20250715003', '003', NEWID(), 'BK20250715003', '13800000003', 0),
    (1, '2034', '003', 2, 1, 1, 2, 1, 'E', '', '', '', '', '', 0),
    
    -- 2楼 - 商务套房
    (1, '2041', '004', 2, 1, 1, 2, 1, 'E', '', '', '', '', '', 0),
    (1, '2042', '004', 2, 1, 1, 2, 1, 'F', 'INV20250715004', '004', NEWID(), 'BK20250715004', '13800000004', 0),
    (1, '2043', '004', 2, 1, 1, 2, 1, 'E', '', '', '', '', '', 0),
    (1, '2044', '004', 2, 1, 1, 2, 1, 'E', '', '', '', '', '', 0);
END

-- 5. 创建测试开房缓存信息
IF NOT EXISTS (SELECT 1 FROM OpenCacheInfo WHERE ShopId = 1)
BEGIN
    DECLARE @Today VARCHAR(8) = CONVERT(VARCHAR(8), GETDATE(), 112);
    DECLARE @CurrentTime VARCHAR(4) = CONVERT(VARCHAR(4), GETDATE(), 114);
    
    -- 为已占用的房间创建开房记录
    INSERT INTO OpenCacheInfo (
        Ikey, BookNo, ShopId, CustKey, CustName, CustTel, ComeDate, ComeTime,
        Beg_Key, Beg_Name, End_Key, End_Name, Numbers, RtNo, RtName,
        CtNo, CtName, PtNo, PtName, BookMemory, BookStatus, CheckinStatus,
        BookShopId, BookUserId, BookUserName, BookDateTime, Invno, Openmemory,
        OrderUserid, OrderUsername, RmNo, Val1
    )
    SELECT 
        r.RoomToIkey,
        r.BookNo,
        r.ShopId,
        NEWID(),
        '测试客户' + r.RmNo,
        r.CustTel,
        @Today,
        @CurrentTime,
        '01', '上午时段',
        '05', '晚上时段',
        2,
        r.RtNo,
        rt.RtName,
        '001', '现金',
        '001', '标准价格',
        '测试预约',
        '1', '1',
        r.ShopId,
        '000001',
        '测试用户',
        GETDATE(),
        r.InvNo,
        '测试开房',
        '000001',
        '测试用户',
        r.RmNo,
        ''
    FROM RmInfo r
    INNER JOIN RtInfo rt ON r.RtNo = rt.RtNo AND r.ShopId = rt.ShopId
    WHERE r.ShopId = 1 AND r.RmsStatus IN ('U', 'F') AND r.RoomToIkey IS NOT NULL AND r.RoomToIkey != '';
END

-- 6. 创建测试转房记录表（如果不存在）
IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'RmExchange')
BEGIN
    CREATE TABLE RmExchange (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        ExchangeDate DATETIME NOT NULL,
        FromRmNo VARCHAR(50) NOT NULL,
        ToRmNo VARCHAR(50) NOT NULL,
        UserName VARCHAR(50) NOT NULL,
        ShopId INT NOT NULL,
        IsExchange BIT DEFAULT 1
    );
END

-- 7. 创建一些历史转房记录
IF NOT EXISTS (SELECT 1 FROM RmExchange WHERE ShopId = 1)
BEGIN
    INSERT INTO RmExchange (ExchangeDate, FromRmNo, ToRmNo, UserName, ShopId, IsExchange)
    VALUES 
    (DATEADD(DAY, -1, GETDATE()), '1011', '2031', '测试用户', 1, 1),
    (DATEADD(DAY, -2, GETDATE()), '1021', '2041', '测试用户', 1, 1),
    (DATEADD(DAY, -3, GETDATE()), '2032', '1022', '测试用户', 1, 1);
END

-- 8. 创建测试价格信息（如果需要）
IF NOT EXISTS (SELECT 1 FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'MRtPrice')
BEGIN
    CREATE TABLE MRtPrice (
        Id INT IDENTITY(1,1) PRIMARY KEY,
        ShopId INT NOT NULL,
        RtNo VARCHAR(10) NOT NULL,
        TimeKey VARCHAR(10) NOT NULL,
        Price DECIMAL(10,2) NOT NULL,
        IsDisplay BIT DEFAULT 1
    );
    
    -- 插入测试价格数据
    INSERT INTO MRtPrice (ShopId, RtNo, TimeKey, Price, IsDisplay)
    VALUES 
    (1, '001', '01', 150.00, 1), -- 标准大床房
    (1, '002', '01', 200.00, 1), -- 豪华大床房
    (1, '003', '01', 500.00, 1), -- 总统套房
    (1, '004', '01', 300.00, 1); -- 商务套房
END

PRINT '测试数据创建完成！';
PRINT '已创建：';
PRINT '- 1个测试门店';
PRINT '- 3个测试区域（1-3楼）';
PRINT '- 4种房型（标准、豪华、总统、商务）';
PRINT '- 16个测试房间';
PRINT '- 4条开房记录';
PRINT '- 3条历史转房记录';
PRINT '';
PRINT '现在可以测试房间转移计费功能了！';

-- 查询创建的测试数据
SELECT '房间状态统计' AS 信息类型, RmsStatus AS 状态, COUNT(*) AS 数量
FROM RmInfo WHERE ShopId = 1
GROUP BY RmsStatus
UNION ALL
SELECT '房型统计', rt.RtName, COUNT(*)
FROM RmInfo r
INNER JOIN RtInfo rt ON r.RtNo = rt.RtNo AND r.ShopId = rt.ShopId
WHERE r.ShopId = 1
GROUP BY rt.RtName;
