# 灵活房间转移计费功能实施文档

## 概述

本文档描述了为RMS2018系统实施的灵活房间转移计费功能。该功能允许前台工作人员在执行房间转移时选择计费方式，支持免费升级和付费升级两种业务场景。

## 业务需求

### 问题描述
原系统在房间转移时缺乏灵活性，无法根据实际业务场景（如免费补偿或付费升级）来决定客户的最终计费方式。

### 解决方案
为前台工作人员提供明确的选择控制，在房间转移操作期间可以选择：
1. **免费升级**：按原房间类型计费
2. **付费升级**：按新房间类型计费

## 技术实现

### 1. UI界面修改

**文件**: `RMS2018\Controls\RoomChange.xaml`

**修改内容**:
- 在转房按钮区域添加了计费方式选择的单选按钮组
- 默认选择"按原房间类型计费（免费升级）"
- 提供"按新房间类型计费（付费升级）"选项

### 2. 后端逻辑修改

**文件**: `RMS2018\Controls\RoomChange.xaml.cs`

**修改内容**:
- 在 `RmExchangeAdd` 方法中添加条件逻辑
- 根据用户选择调用不同的存储过程：
  - 免费升级：调用 `GetMRmInfo_Up_KeepRate`
  - 付费升级：调用原有的 `GetMRmInfo_Up`

### 3. 数据访问层修改

**文件**: 
- `RMSDao\RMS\MRmInfoDal.cs`
- `RMSBLL\RMS\MRmInfoBll.cs`

**修改内容**:
- 添加新方法 `GetMRmInfo_Up_KeepRate` 支持保持原计费房型的转房操作

### 4. 数据库存储过程

**新增存储过程**: `GetMRmInfo_Udp_KeepRate`

**功能**:
- 只更新 `OpenCacheInfo` 表中的物理房号（`RmNo`）和转房来源（`FromRmNo`）
- 保持计费房型（`RtNo`, `RtName`）不变
- 确保客户按原始约定的房型标准计费

## 部署步骤

### 1. 数据库部署
1. 执行 `Database_Scripts/GetMRmInfo_Udp_KeepRate.sql` 创建新的存储过程
2. 确保存储过程创建成功并测试基本功能

### 2. 应用程序部署
1. 编译并部署修改后的应用程序
2. 确保所有依赖项正确更新

### 3. 测试验证
1. 测试免费升级场景
2. 测试付费升级场景
3. 验证计费逻辑正确性

## 测试场景

### 场景1：免费升级
**前提条件**:
- 客户原本预订豪华大床房（200元/小时）
- 因设施问题需要转移到总统套房（500元/小时）
- 酒店提供免费升级作为补偿

**操作步骤**:
1. 在转房界面选择"按原房间类型计费（免费升级）"
2. 执行转房操作
3. 验证客户仍按豪华大床房标准计费

**预期结果**:
- 客户物理位置更新为总统套房
- 计费标准保持为豪华大床房（200元/小时）
- `OpenCacheInfo` 表中 `RmNo` 更新，`RtNo` 和 `RtName` 保持不变

### 场景2：付费升级
**前提条件**:
- 客户原本预订普通大床房（150元/小时）
- 客户主动要求升级到豪华大床房（200元/小时）
- 客户同意支付升级费用

**操作步骤**:
1. 在转房界面选择"按新房间类型计费（付费升级）"
2. 执行转房操作
3. 验证客户按新房间标准计费

**预期结果**:
- 客户物理位置更新为豪华大床房
- 计费标准更新为豪华大床房（200元/小时）
- `OpenCacheInfo` 表中 `RmNo`、`RtNo` 和 `RtName` 全部更新

## 注意事项

1. **数据库权限**: 确保应用程序具有执行新存储过程的权限
2. **事务处理**: 所有转房操作都在事务中执行，确保数据一致性
3. **日志记录**: 系统会记录转房操作的详细日志，包括选择的计费方式
4. **向后兼容**: 现有功能保持不变，新功能为可选增强

## 维护说明

1. **监控**: 定期检查转房操作日志，确保功能正常运行
2. **备份**: 在部署前备份相关数据库和应用程序
3. **培训**: 为前台工作人员提供新功能的使用培训

## 联系信息

如有技术问题或需要支持，请联系开发团队。
