# 本地测试环境配置指南

## 问题描述

在本地测试环境中，由于没有线上的房间数据，无法直接测试房间转移计费功能。本指南提供了三种解决方案来创建测试数据。

## 解决方案

### 方案一：使用本地数据库 + 测试数据（推荐）

这是最接近生产环境的测试方法。

#### 步骤1：配置本地数据库

1. **安装SQL Server**（如果尚未安装）
   - 下载SQL Server Express（免费版本）
   - 安装时选择默认实例

2. **创建数据库**
   ```sql
   CREATE DATABASE rms2019;
   ```

3. **创建必要的表结构**
   - 复制线上数据库的表结构
   - 或使用提供的建表脚本

#### 步骤2：执行测试数据脚本

运行 `Database_Scripts/Create_Test_Data.sql` 脚本：

```sql
-- 这个脚本会创建：
-- - 1个测试门店
-- - 3个测试区域（1-3楼）
-- - 4种房型（标准、豪华、总统、商务）
-- - 16个测试房间
-- - 4条开房记录
-- - 3条历史转房记录
```

#### 步骤3：修改连接字符串

在 `RMS2018/App.config` 中修改数据库连接：

```xml
<connectionStrings>
  <add name="dbfoodConn" connectionString="Data Source=localhost;Initial Catalog=rms2019;User ID=sa;Password=************"/>
</connectionStrings>

<appSettings>
  <add key="TestMode" value="true"/>
  <add key="RmsServerName" value="localhost"/>
  <add key="shopid" value="1"/>
</appSettings>
```

#### 步骤4：修改DbHelp_rms2019.cs

在 `RMSDao/DbHelp_rms2019.cs` 中确保连接字符串正确：

```csharp
dao.connstr = $"Data Source=localhost;Initial Catalog=rms2019;User ID=sa;Password=************";
```

### 方案二：使用内存模拟数据

如果无法配置本地数据库，可以使用内存模拟数据。

#### 步骤1：启用测试模式

在 `RMS2018/App.config` 中：

```xml
<appSettings>
  <add key="TestMode" value="true"/>
</appSettings>
```

#### 步骤2：使用模拟数据服务

系统已经创建了 `MockDataService` 类，可以在内存中生成测试数据：

- 16个测试房间（不同状态：空闲、占用、清扫、维修）
- 4种房型（标准、豪华、总统、商务）
- 模拟的开房记录和转房历史

#### 步骤3：测试转房功能

模拟数据会自动处理转房操作，无需真实的数据库连接。

### 方案三：使用SQLite本地数据库

如果不想安装SQL Server，可以使用SQLite。

#### 步骤1：安装SQLite NuGet包

```
Install-Package System.Data.SQLite
```

#### 步骤2：创建SQLite数据库文件

创建一个本地的 `.db` 文件，包含必要的表结构和测试数据。

## 测试场景

### 测试场景1：免费升级

1. **前提条件**：
   - 客户在标准大床房（房号1012）
   - 需要转移到豪华大床房（房号1021）
   - 因设施问题提供免费升级

2. **操作步骤**：
   - 打开转房界面
   - 选择转出房间：1012
   - 选择转入房间：1021
   - 选择"按原房间类型计费（免费升级）"
   - 点击转房

3. **预期结果**：
   - 客户物理位置更新为1021
   - 计费仍按标准大床房标准
   - 系统日志显示"免费升级"

### 测试场景2：付费升级

1. **前提条件**：
   - 客户在标准大床房（房号1012）
   - 客户要求升级到总统套房（房号2031）
   - 客户同意支付升级费用

2. **操作步骤**：
   - 打开转房界面
   - 选择转出房间：1012
   - 选择转入房间：2031
   - 选择"按新房间类型计费（付费升级）"
   - 点击转房

3. **预期结果**：
   - 客户物理位置更新为2031
   - 计费更新为总统套房标准
   - 系统日志显示"付费升级"

## 验证方法

### 1. 数据库验证

检查 `OpenCacheInfo` 表：

```sql
-- 查看转房前后的数据变化
SELECT Ikey, RmNo, RtNo, RtName, FromRmNo 
FROM OpenCacheInfo 
WHERE ShopId = 1;
```

### 2. 日志验证

查看系统日志文件，确认转房操作记录：
- 免费升级：应显示"房转房操作成功（免费升级）"
- 付费升级：应显示"房转房操作成功（付费升级）"

### 3. UI验证

在转房界面中：
- 确认单选按钮正常显示
- 确认默认选择"免费升级"
- 确认可以切换选择

## 故障排除

### 问题1：数据库连接失败

**解决方案**：
1. 检查SQL Server服务是否启动
2. 确认连接字符串中的服务器名称、数据库名称、用户名和密码
3. 确认防火墙设置允许数据库连接

### 问题2：没有房间数据

**解决方案**：
1. 确认已执行测试数据脚本
2. 检查门店ID是否正确（应为1）
3. 确认表结构是否完整

### 问题3：转房操作失败

**解决方案**：
1. 检查存储过程是否已创建
2. 确认房间状态是否允许转房
3. 查看错误日志获取详细信息

## 注意事项

1. **数据安全**：测试数据仅用于开发环境，不要在生产环境中使用
2. **性能考虑**：模拟数据在内存中运行，重启应用后会重置
3. **功能限制**：某些高级功能可能需要完整的数据库环境才能正常工作

## 联系支持

如果在配置过程中遇到问题，请：
1. 检查错误日志
2. 确认配置文件设置
3. 联系开发团队获取支持
