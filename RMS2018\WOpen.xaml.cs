﻿using RMS2018.Utils.RmLock;
using RMSBLL.DbFood;
using RMSBLL.MIMS;
using RMSBLL.RMS;
using RMSBLL.Song;
using RMSModel;
using RMSModel.DbFood;
using RMSModel.ExtensionRMS;
using RMSModel.MIMS;
using RMSModel.RMS;
using RMSModel.TCP;
using RMSUtils.Print;
using RMSUtils.RMS;
using RMSUtils.WebApi;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;


//using System.IO;
//using System.Net;
using Newtonsoft.Json;
using System.Runtime.Serialization;
using System.Web.Script.Serialization;
using RMS2018.birthday_giftPay;
using RMS2018.lib.Factory;
using RMSUtils.Common;

namespace RMS2018
{
    /// <summary>
    /// WOpen.xaml 的交互逻辑
    /// </summary>
    public partial class WOpen : Window
    {
        /// <summary>
        /// 所属门店
        /// </summary>
        int shopid = 0;

        SingleRun single;
        /// <summary>
        ///  上一个选择房间
        /// </summary>
        MRm_Rt_MArea_MShop oldShop;
        /// <summary>
        /// 当前选择的房号
        /// </summary>
        MRm_Rt_MArea_MShop shopRoom;

        /// <summary>
        /// 顾客信息
        /// </summary>
        MCustInfo custinfo;
        /// <summary>
        /// 预约信息
        /// </summary>
        bookcacheinfo_preorder book;
        /// <summary>
        /// 当前时段
        /// </summary>
        public MShopTimeInfoJoin shoptime;
        /// <summary>
        /// 当前时段集合
        /// </summary>
        List<MShopTimeInfoJoin> shoptimeList;
        /// <summary>
        /// 适合的时段集合
        /// </summary>
        List<MShopTimeInfoJoin> shoptimeUseList;
        /// <summary>
        /// 适合的时段集合历史
        /// </summary>
        List<MShopTimeInfoJoin> shoptimeUseListOld = new List<MShopTimeInfoJoin>();
        /// <summary>
        /// 会员信息
        /// </summary>
        MemberInfo memberinfo = null;

        /// <summary>
        /// 开房/预约key
        /// </summary>
        string ikey;
        /// <summary>
        /// 当前时段绑定预约数据源
        /// </summary>
        List<bookcacheinfo_preorder> datalist;

        public WOpen()
        {
            InitializeComponent();
            single = SingleRun.GetSingle();
            single.wopen = this;
            this.DataContext = single;
            uRoom.menuItemClick += URoom_menuItemClick;//右键菜单点击
            uRoom.checkedChang = CheckedChang;// 房号选择变更绑定
            openRoom.btnCustUpdate.Click += BtnCustUpdate_Click;
            openRoom.btnCustFill.Click += btnCustFill_Click;//回绑点击按钮
            refill.Btn_Confim.Click += Btn_Confim_Click;//确定回绑事件
            refill.btnSearch.Click += btnSearch_Click;
            refill.tbPhone.KeyUp += tbPhone_KeyUp;
            updcust.eClose += eClose;
            SetInit();
        }
        public void eClose()
        {
            CheckedChang(null);
        }
        /// <summary>
        /// 数据初始化
        /// </summary>
        public void SetInit()
        {
            try
            {
                uRoom.SetUiModel();

                // 安全获取shopid
                shopid = single?.vmpc?.CfixedConfig?.ShopId ?? 1;

                // 安全初始化房型信息
                try
                {
                    var rtInfo = single?.vmpc?.vmRtInfo?.GetRtInfo(shopid);
                    if (rtInfo != null && rtInfo.Count > 0)
                    {
                        cbRoomType.ItemsSource = rtInfo;
                        cbRoomType.SelectedIndex = 0;
                    }
                    else
                    {
                        // 创建默认房型
                        var defaultRtInfo = new List<MRt_MShop>
                        {
                            new MRt_MShop { RtNo = "001", RtName = "标准房", ShopId = shopid }
                        };
                        cbRoomType.ItemsSource = defaultRtInfo;
                        cbRoomType.SelectedIndex = 0;
                    }
                }
                catch (Exception rtEx)
                {
                    System.Diagnostics.Debug.WriteLine($"房型初始化失败: {rtEx.Message}");
                    // 使用最基本的默认房型
                    cbRoomType.ItemsSource = new List<MRt_MShop>
                    {
                        new MRt_MShop { RtNo = "001", RtName = "默认房型", ShopId = shopid }
                    };
                    cbRoomType.SelectedIndex = 0;
                }

                // 安全初始化时段信息
                try
                {
                    shoptimeList = single?.vmpc?.vmShopTimeInfo?.GetNewTime_Open(shopid, VMTimeDate.GetWordDate());
                    SetChangeTime();//换挡检测
                }
                catch (Exception timeEx)
                {
                    System.Diagnostics.Debug.WriteLine($"时段初始化失败: {timeEx.Message}");
                    // 使用默认时段
                    shoptimeList = new List<MShopTimeInfoJoin>();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("初始化失败：" + ex.Message, "提示", MessageBoxButton.OK, MessageBoxImage.Error);

                // 确保基本功能可用
                shopid = 1;
                cbRoomType.ItemsSource = new List<MRt_MShop>
                {
                    new MRt_MShop { RtNo = "001", RtName = "默认房型", ShopId = shopid }
                };
                cbRoomType.SelectedIndex = 0;
                shoptimeList = new List<MShopTimeInfoJoin>();
            }
        }

        /// <summary>
        /// 清空填写信息
        /// </summary>
        public void SetClearInput(bool ClearTel = true)
        {
            if (book != null)
            {
                book = null;
                tbBookMemory.Text = string.Empty;
                tbOpenMemory.Text = string.Empty;
            }
            tbName.Text = "";
            if (ClearTel == true)
            {
                tbTel.Text = "";
            }
            bookNumber.Text = "2";
            tbOpenMemory.Text = "";
            tbBookMemory.Text = "";
            dgBook.SelectedIndex = -1;
            tbBookRt.Text = string.Empty;
            custinfo = null;
        }
        /// <summary>
        /// grid数据刷新
        /// </summary>
        public void SetGridRefresh()
        {
            if (shoptime != null)
            {
                // datalist = MBookCacheInfoBll.GetBookByNoDel(shopid, VMTimeDate.GetWordDate().ToString("yyyyMMdd"), shoptime.TimeNo);
                datalist = MBookCacheInfoBll.GetBookByNoDel1(shopid, VMTimeDate.GetWordDate().ToString("yyyyMMdd"), shoptime.TimeNo);

                foreach (var item in datalist)
                {
                    item.BookShopName = VMShopInfo.GetShopInfo(item.BookShopId).ShopName;
                }

                dgBook.ItemsSource = datalist;
                if (datalist != null)
                {
                    tbBookTot.Text = datalist.Count.ToString();
                    tbBookOut.Text = datalist.Count(i => i.CheckinStatus == 1).ToString();
                    tbBookDiscount.Text = datalist.Count(i => i.Val1 == 1).ToString();
                    tbDeposit.Text = datalist.Count(i => i.DepositTot > 0).ToString();
                    tbPreorder.Text = datalist.Count(i => i.Statu > 0).ToString();
                }
            }
        }

        /// <summary>
        /// 预约时段超时检测
        /// </summary>
        public void SetOutTimeCheck()
        {
            try
            {
                int comtime = int.Parse(DateTime.Now.AddMinutes(-11).ToString("HHmmss"));
                int SuccessCount = 0;
                if (datalist != null)
                {
                    foreach (var item in datalist.FindAll(i => i.CheckinStatus != 1))
                    {
                        if (comtime > item.ComeTimeInt)
                        {
                            item.CheckinStatus = 1;
                            //  MBookCacheInfoBll.SetUpdateData(item);
                            SuccessCount++;

                        }
                    }
                }
                if (SuccessCount > 0)
                {
                    this.Dispatcher.Invoke(new Action(() =>
                    {
                        dgBook.ItemsSource = datalist.OrderBy(i => i.CheckinStatus).ThenBy(i => i.CustTel).ToList();
                        tbBookOut.Text = SuccessCount.ToString();
                    }));

                }
            }
            catch
            {
            }
        }

        /// <summary>
        /// 换档检测
        /// </summary>
        public void SetChangeTime()
        {
            try
            {
                shoptimeUseList = single.vmpc.vmShopTimeInfo.GetNowTime(shoptimeList);
                //if (shoptimeUseList == null)
                //{//首次加载则使用默认跳档时间
                //    shoptimeUseList = single.vmpc.vmShopTimeInfo.GetNowTime(shoptimeList);
                //}
                //else
                //{
                //    MShopTimeInfo shoptime = shoptimeUseList[shoptimeUseList.Count - 1];
                //    if (shoptime != null)
                //    {
                //        shoptimeUseList = single.vmpc.vmShopTimeInfo.GetNowTime(shoptimeList, shoptime.ChangeMinute);
                //    }
                //}
                if (shoptimeUseList != null)
                {
                    int count = 1;
                    if (shoptimeUseListOld != null)
                    {
                        count = shoptimeUseList.Except(shoptimeUseListOld).Count();
                    }
                    if (count > 0 || shoptimeUseListOld.Count != shoptimeUseList.Count)
                    {
                        this.Dispatcher.Invoke(new Action(() =>
                        {
                            //  shoptime = shoptimeUseList.FirstOrDefault();
                            tbShopTime.ItemsSource = shoptimeUseList;
                            tbShopTime.IsEnabled = shoptimeUseList.Count > 1;
                            tbShopTime.SelectedIndex = shoptimeUseList.Count - 1;
                            shoptimeUseListOld = shoptimeUseList;
                        }));
                    }
                }
            }
            catch
            {
            }

        }
        /// <summary>
        /// 绑定顾客预订信息
        /// </summary>
        public void SetCustInfo(MCustInfo custinfo)
        {
            if (custinfo != null)
            {
                tbName.Text = custinfo.CustName;
                tbTel.Text = custinfo.CustTel;
                rbSex1.IsChecked = (custinfo.CustSex == "先生");
                rbSex2.IsChecked = !rbSex1.IsChecked;

                try
                {

                    if (book != null)
                    {//   绑定会员信息如果是预订顾客，则在云端服务器获取，否则在总部服务器获取
                        memberinfo = RmsmemberinfoBll.GetMemberInfo(book.Ikey);
                    }
                    else
                    {
                        if (WindowAbility.VipDistinguish == true)
                        {
                            memberinfo = MemberInfoBll.GetMemberInfo(tbTel.Text);
                        }

                    }
                }
                catch
                {

                }
                if (memberinfo != null)
                {
                    tabCustInfo.SelectedIndex = 2;
                }
                single.vmpc.vmCustInfo.mCustInfoCall = custinfo;
                if (single.vmpc.vmCustInfo.mCustInfoCall.BechaNumber > 0)
                {//打开顾客行为
                    btnCustBehaInfo_Click(null, null);
                }
                if (shopRoom != null)
                {//以防操作人员误选当房，当顾客资料发生变更时重新选择房型信息
                    cbRoomType.SelectedValue = shopRoom.RtNo;
                }

            }
            umemberinfo.DataContext = memberinfo;

        }
        /// <summary>
        /// 获取顾客信息
        /// </summary>
        /// <returns></returns>
        public MCustInfo GetCustInfo()
        {
            if (custinfo == null)
            {
                custinfo = new MCustInfo() { Custcompany = string.Empty, OpenId = string.Empty };
            }
            custinfo.CustName = tbName.Text;
            custinfo.CustTel = tbTel.Text;
            custinfo.CustSex = rbSex1.IsChecked == true ? rbSex1.Content.ToString() : rbSex2.Content.ToString();
            return custinfo;
        }
        /// <summary>
        /// 获取开房信息
        /// </summary>
        public MOpenCacheInfo GetOpenInfo()
        {
            try
            {
                MRt_MShop rtinfo = cbRoomType.SelectedItem as MRt_MShop;
                MConTypeInfo contype = cbConType.SelectedItem as MConTypeInfo;
                WeChatUser wechatUser = uwechatSearch.GetSelectUser();
                if (contype == null)
                {
                    MessageBox.Show("消费类型获取失败，请重新选择再试!", "提示", MessageBoxButton.OK, MessageBoxImage.Error);
                    return null;
                }
                if (rtinfo == null)
                {
                    MessageBox.Show("当房房型获取失败，请重新选择再试!", "提示", MessageBoxButton.OK, MessageBoxImage.Error);
                    return null;
                }

                int systemTime = int.Parse(DateTime.Now.ToString("HHmmss"));
                int ComnTime = int.Parse(dtpComtime.TimeData.Replace(":", ""));
                MOpenCacheInfo opencache = new MOpenCacheInfo()
                {
                    CustTel = tbTel.Text,
                    CustName = tbName.Text + (rbSex1.IsChecked == true ? rbSex1.Content.ToString() : rbSex2.Content.ToString()),
                    ComeDate = VMTimeDate.GetWordDate().ToString("yyyyMMdd"),
                    ComeTime = DateTime.Now.ToString("HH:mm:ss"),//(systemTime < 50000 || systemTime > ComnTime) ? DateTime.Now.ToString("HH:mm:ss") : dtpComtime.TimeData,
                    Numbers = bookNumber.Number,
                    RtNo = rtinfo.RtNo,
                    RtName = rtinfo.RtName,
                    CtNo = contype.CtNo,
                    CtName = contype.CtName,
                    OpenMemory = tbOpenMemory.Text,
                    BookMemory = tbBookMemory.Text,
                    Beg_Key = shoptime.TimeNo,
                    Beg_Name = shoptime.TimeName,
                    PtNo = 1,
                    PtName = "PT类型",
                    Val1 = cbDiscount.IsChecked == true ? 1 : 0,
                    OrderUserID = wechatUser.UserId,
                    OrderUserName = wechatUser.name

                };
                return opencache;
            }
            catch (Exception ex)
            {
                MessageBox.Show("提示", "获取开房信息过程发生异常：" + ex.Message, MessageBoxButton.OK, MessageBoxImage.Error);
                return null;
            }


        }
        /// <summary>
        /// 绑定房间信息
        /// </summary>
        public void SetBingRoomInfo(MRm_Rt_MArea_MShop shop)
        {
            if (shop != null)
            {
                tbRtName.Text = shop.RtName;
                tbRmno.Text = shop.RmNo;

            }
        }

        /// <summary>
        /// 房号选择变更时间
        ///    1：让同店设备锁定房间
        ///    2：绑定房间信息
        /// </summary>
        /// <param name="shop"></param>
        public void CheckedChang(MRm_Rt_MArea_MShop shop)
        {
            if (shop != null)
            {
                string oldRmNo = oldShop == null ? string.Empty : oldShop.RmNo;
                /// single.vmpc.client.GetSQL(new RMSModel.TCP.TcpRequest() { Conent = shopid + "," + oldRmNo + "," + shop.RmNo, Request = 2 });//旧的锁定模式
                RmLock_utils.LockRoom(single.vmpc.vmuserinfo.muserinfo.UserId, shop.RmNo);
                this.shopRoom = shop;
                SetBingRoomInfo(shop);
                oldShop = shop;
                cbRoomType.SelectedValue = shop.RtNo;
                tabItem01.Header = shop.RmNo + "房";
                CheckedChangBinging(shop);
                //2020-12-15 jjy新增 占用、结账跟续单状态是展示回绑按钮
                if (shop.RoomToIkey == "" && (shop.RmsStatus == "U" || shop.RmsStatus == "C" || shop.RmsStatus == "A" || shop.RmsStatus == "F"))
                    openRoom.btnCustFill.Visibility = Visibility.Visible;
                else
                    openRoom.btnCustFill.Visibility = Visibility.Collapsed;
                // openRoom.btnCustFill.Visibility = Visibility.Visible;

            }
            else
            {
                shop = oldShop;
                this.Dispatcher.Invoke(new Action(() =>
                {
                    CheckedChangBinging(shop);
                }));
            }




        }
        public void CheckedChangBinging(MRm_Rt_MArea_MShop shop)
        {
            if (shop != null)
            {
                MOpenCacheInfo openCache = null;
                MBookCacheInfo bookCache = null;
                if (string.IsNullOrEmpty(shop.RoomToIkey) == false)
                {
                    openCache = OpenCacheInfoBll.GetDataByIkey(shop.RoomToIkey);
                    if (openCache != null)
                    {
                        bookCache = MBookCacheInfoBll.GetBookKey(openCache.Ikey);
                        if (WindowAbility.VipDistinguish == true)
                        {
                            try
                            {
                                MemberInfo memberinfo = RmsmemberinfoBll.GetMemberInfo(openCache.Ikey);
                                umemberinfo.DataContext = memberinfo;
                            }
                            catch
                            {
                            }
                        }
                    }
                    // bookCache = MBookCacheInfoBll.GetBookKey(shop.RoomToIkey);

                }
                else
                {
                    string Remark = null;

                    if (shop.RmsStatus == "L" || shop.RmsStatus == "B" || shop.RmsStatus == "R")
                    {
                        Remark = MRmInfoBll.GetMRmInfo_Remark(single.vmpc.vmuserinfo.ShopId, shop.RmNo);
                    }


                    string defaultString = "---";
                    openCache = new MOpenCacheInfo()
                    {
                        Beg_Name = defaultString,
                        BookMemory = Remark == null ? defaultString : Remark,
                        End_Name = defaultString,
                        BookNo = defaultString,
                        BookUserName = defaultString,
                        ComeDate = defaultString,
                        ComeTime = defaultString,
                        CtName = defaultString,
                        CustName = defaultString,
                        CustTel = defaultString,
                        RtName = defaultString,
                        OrderUserName = defaultString


                    };
                    bookCache = openCache;
                }

                openRoom.DataContext = openCache;
                bookRoom.DataContext = bookCache;
                ///启用按钮
                if (shop.RmsStatus == "E")
                {
                    btnRoomTo.IsEnabled = true;
                    btnOpen.IsEnabled = true;


                }
                else
                {
                    btnOpen.IsEnabled = false;
                    btnRoomTo.IsEnabled = false;
                }
                btnOpenTwo.IsEnabled = btnOpen.IsEnabled;

                btnRoomToRefresh.IsEnabled = shop.RmsStatus == "W";

                tabCustInfo.SelectedIndex = 1;
            }

        }
        /// <summary>
        /// 信息回调
        /// </summary>
        public void SetCallback(TcpReponse model)
        {
            this.Dispatcher.Invoke(new Action(() =>
            {
                if (model != null && model.Conent != null && model.Conent.Length > 15)
                {
                    SetBookLock(model.Conent);
                }
                else
                {
                    //     uRoom.SetRoomLock(model.Conent);
                }

            }));

        }
        /// <summary>
        /// 锁定预约记录
        /// </summary>
        public void SetBookLock(string bookIkey)
        {
            try
            {

                List<MBookCacheInfo> list = dgBook.ItemsSource as List<MBookCacheInfo>;
                //20250703-lsf,加上空值判断
                if (list == null)
                {
                    return;
                }
                foreach (var item in list)
                {
                    if (item.Ikey == bookIkey)
                    {
                        SetGridRefresh();
                        break;
                    }

                }
            }
            catch
            {

            }
        }
        /// <summary>
        /// 绑定顾客预约信息
        /// </summary>
        /// <param name="book"></param>
        public void SetBookInfo(MBookCacheInfo book)
        {
            if (book != null)
            {

                bookNumber.Text = book.Numbers.ToString();
                tbBookMemory.Text = book.BookMemory;
                tbBookRt.Text = book.RtName;
                tbEnd_Name.Text = book.End_Name;
                tbEnd_Time.Text = book.ComeTime;
                cbConType.SelectedValue = book.CtNo;
                cbDiscount.IsChecked = book.Val1 == 1;
                if (string.IsNullOrEmpty(book.OrderUserID) == false)
                {
                    uwechatSearch.SetConentBing(book.OrderUserID, book.OrderUserName);
                }
                else
                {
                    uwechatSearch.SetClearInputConent();
                }
                try
                {
                    // int ComnTime = int.Parse(book.ComeTime.Replace(":", ""));

                    int systemTime = int.Parse(DateTime.Now.ToString("HHmmss"));
                    int systemTimeNOT_s = int.Parse(DateTime.Now.ToString("HHmm"));

                    #region 改全时段使用系统时间
                    dtpComtime.SetTimeDate(DateTime.Now.ToString("HH:mm:ss"));

                    //if (shoptime == null || systemTime >= 210000 || systemTime <= 50000)
                    //{
                    //    dtpComtime.SetTimeDate(DateTime.Now.ToString("HH:mm:ss"));
                    //}
                    //else
                    //{
                    //    dtpComtime.SetTimeDate(systemTimeNOT_s > shoptime.BegTime ? systemTime : shoptime.BegTime);
                    //}
                    #endregion

                }
                catch
                {

                }

                SetScreenRt(book.RtNo);



            }
        }
        /// <summary>
        /// 检测输入的信息
        /// </summary>
        public bool SetInputInfoCheck()
        {
            MCustInfo custinfo = GetCustInfo();
            if (tbTel.Islegitimate == false)
            {
                MessageBox.Show("请完善顾客信息!");
                return false;
            }
            if (shopRoom == null)
            {
                MessageBox.Show("请选择房间!");
                return false;
            }
            return true;

        }
        /// <summary>
        /// 推荐房型信息
        /// </summary>
        List<MRm_Rt_MArea_MShop> ScreenRtList;
        /// <summary>
        /// 房型筛选
        ///      匹配所有所有相同房型，并高亮显示，默认选中第一个房间
        /// </summary>
        /// <param name="rtno"></param>
        public void SetScreenRt(string rtno)
        {
            SetScreenRtCancel();
            ScreenRtList = uRoom.GetRtRoomByRtNo(rtno);
            if (ScreenRtList != null)
            {
                foreach (var item in ScreenRtList)
                {
                    if (item.RmsStatus == "E")
                    {
                        item.BackgroundColor = new SolidColorBrush(Colors.LightCyan);
                    }

                }
            }

        }
        /// <summary>
        /// 房型筛选取消
        ///     
        /// </summary>
        /// <param name="rtno"></param>
        public void SetScreenRtCancel()
        {
            if (ScreenRtList != null)
            {
                foreach (var item in ScreenRtList)
                {
                    if (item.RmsStatus == "E")
                    {
                        item.BackgroundColor = new SolidColorBrush(Colors.Transparent);
                    }

                }
                ScreenRtList = null;
            }

        }
        private void btnBook_Click(object sender, RoutedEventArgs e)
        {
            // single.SetWindowOpen(EWindowUi.预约);
            WindowBuildFactory.CreateWindow(EWindowUi.预约);

            this.Hide();
        }
        /// <summary>
        /// 预约表格点击事件
        ///         点击两次绑定顾客信息
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void dgBook_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {

            book = dgBook.SelectedItem as bookcacheinfo_preorder;
            if (book != null)
            {
                if (tbTel.Text != book.CustTel)
                {
                    tbTel.Text = book.CustTel;
                }
                else
                {
                    tbName.Text = book.CustName.Replace("先生", "").Replace("女士", "");
                    SetBookInfo(book);
                }
            }
        }

        /// <summary>
        /// 搜索grid预订信息
        /// </summary>
        public void GetGridBookInfo(string conent)
        {
            bookcacheinfo_preorder mbook = dgBook.SelectedItem as bookcacheinfo_preorder;
            if (mbook != null && mbook.CustTel == conent)
            { dgBook.ScrollIntoView(mbook); book = mbook; SetBookInfo(mbook); return; }
            List<bookcacheinfo_preorder> list = dgBook.ItemsSource as List<bookcacheinfo_preorder>;
            if (list != null)
            {
                foreach (var item in list)
                {
                    if (item.CustTel == conent)
                    {
                        dgBook.SelectedItem = item;
                        dgBook.ScrollIntoView(item);
                        book = item;
                        SetBookInfo(item);
                        break;
                    }
                }
            }
            //return false;
        }

        ///// <summary>
        ///// 注销程序
        ///// </summary>
        ///// <param name="sender"></param>
        ///// <param name="e"></param>
        //private void btnClose_Click(object sender, RoutedEventArgs e)
        //{

        //    bool result = Msg.showDialog("确定退出程序？");
        //    if (result == true)
        //    {
        //        single.vmpc.client.SetLoginInfo_Off();
        //        Environment.Exit(0);
        //    }
        //}

        private void btnSms_Click(object sender, RoutedEventArgs e)
        {
            WindowBuildFactory.CreateWindow(EWindowUi.信息);

            //single.SetWindowOpen(EWindowUi.信息);
            btnMsgEllipse.Visibility = Visibility.Collapsed;
        }

        bookcacheinfo_preorder bookRecommend = null;//推荐的预订信息
        MRm_Rt_MArea_MShop roomRecommend = null;//推荐的房间号
        /// <summary>
        /// 搜索按钮监控事件
        ///   当触发回车并且长度大于4，则执行搜索
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tbSearch_KeyDown(object sender, KeyEventArgs e)
        {
            //if (e.Key == Key.Enter)
            //{
            try
            {
                searchTitle.Text = "";
                if (bookRecommend != null)
                {
                    bookRecommend.BookRecommend = false;
                    bookRecommend.FontColor = Brushes.Black;
                    bookRecommend = null;
                }
                if (roomRecommend != null)
                {
                    roomRecommend.BorderThickness = 0;
                    roomRecommend = null;
                }
                string search = tbSearch.Text;

                if (e.Key == Key.Enter)
                {
                    List<MWSearchList> searchList = new List<MWSearchList>();

                    List<bookcacheinfo_preorder> list = dgBook.ItemsSource as List<bookcacheinfo_preorder>;

                    if (list != null)
                    {
                        foreach (var item in list)
                        {
                            if ((item.CustTel.Contains(search) || item.BookNo == search))
                            {
                                if (searchList.Count == 0)
                                {
                                    item.BookRecommend = true;
                                    //item.SetGridColor();
                                    item.SetGridColor_New();
                                    dgBook.ScrollIntoView(item);
                                    bookRecommend = item;
                                    searchTitle.Text = "在预约记录找到相关信息";
                                    tbTel.Text = item.CustTel;


                                    //   dgBook.SelectedItem = item;
                                }
                                searchList.Add(new MWSearchList() { SelectObject = item, BookNo = item.BookNo, CustName = item.CustName, CustTel = item.CustTel });


                            }
                        }
                    }
                    List<MRm_Rt_MArea_MShop> mroomList = uRoom.GetBookNoAndTel(search);
                    MRm_Rt_MArea_MShop mroom = null;

                    if (mroomList != null)
                    {
                        int index = 0;
                        foreach (var item in mroomList)
                        {
                            if (index == 0)
                            {
                                mroom = item;
                            }
                            index++;
                            if (item.rminsostatus != null)
                            {
                                MRmInfoStatus status = item.rminsostatus;
                                searchList.Add(new MWSearchList() { SelectObject = item, BookNo = item.BookNo, CustName = status.CustName, CustTel = status.CustTel, RmNo = status.RmNo });
                            }

                        }
                    }

                    if (mroom != null)
                    {
                        mroom.BorderThickness = 3;
                        roomRecommend = mroom;
                        searchTitle.Text = "顾客已开房：" + mroom.RmNo;
                    }
                    if (searchList.Count == 0)
                    {
                        SetClearInput();
                    }
                    if (searchList.Count > 1)
                    {
                        WSearchList wsearchList = new WSearchList();
                        wsearchList.SetBingingGrid(searchList);
                        wsearchList.ShowDialog();
                        if (wsearchList.book != null)
                        {
                            dgBook.SelectedItem = wsearchList.book;
                            dgBook.ScrollIntoView(wsearchList.book);
                            tbTel.Text = wsearchList.book.CustTel;
                        }
                        searchTitle.Text = "多条记录";
                    }


                }
            }
            catch (Exception ex)
            {

                MessageBox.Show("查询过程发生异常：" + ex.Message);
            }


        }
        public void SetNewMsg(MSMS _sms)
        {
            usmsinfo.SetMsg(_sms);
            btnMsgEllipse.Visibility = Visibility.Visible;
        }
        /// <summary>
        /// 开房操作
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnOpen_Click(object sender, RoutedEventArgs e)
        {

            try
            {
                //开房操作参数
                OpenOperateProperty property = new OpenOperateProperty();
                Button btn = sender as Button;
                if (btn != null && btn.Tag != null)
                {
                    property.IsPrint = false;
                }
                bool checkResult = SetInputInfoCheck();
                if (checkResult)
                {
                    MOpenCacheInfo openCache = GetOpenInfo();
                    if (openCache != null)
                    {
                       /* bool value = (bool)Application.Current.Properties["_annualCard"];
                        if (value == false)
                        {
                            book.DemandNumber = 0;
                        }
                       */
                        if (book != null && openCache != null)
                        {

                            openCache.DemandNumber = book.DemandNumber;
                        }
                        single.vmpc.vmuserinfo.SetCustOpenRoom(GetCustInfo(), shopRoom, openCache, book, true, property);
                        if (book != null)
                        {
                            single.vmpc.client.GetSQL(new RMSModel.TCP.TcpRequest() { Conent = book.Ikey, Request = 2 });
                        }
                        if (memberinfo != null)
                        {

                            //memberinfo.BookIkey = openCache.Ikey;
                            //RmsmemberinfoBll.SetInsertData(memberinfo);
                        }
                        //  uRoom.SetChangeStatus(shopRoom.RmNo, "U");

                        //2020-11-10  jjy 新增，已经预下单的顾客直接下单
                        #region   预下单功能
                        if (book != null)
                        {
                            if (book.Statu > 0)
                            {
                                List<PreorderInfo> preorderinfo = preorderBll.GetPreorderInfo(new { CustKey = custinfo.CustKey, type = 0 });
                                if (preorderinfo != null && preorderinfo.Count > 0)
                                {
                                    try
                                    {
                                        #region json字符串反序列化
                                        string str = preorderinfo[0].PreJson;
                                        var list = JsonConvert.DeserializeObject<RMSBLL.DbFood.preorderBll.Onedata>(str);
                                        #endregion
                                        #region 下单
                                        OrderBillNew orderBillNew1 = new OrderBillNew() { CashUserId = single.vmpc.vmuserinfo.muserinfo.UserId, InputUserId = single.vmpc.vmuserinfo.muserinfo.UserId, RmNo = openCache.RmNo, CashType = "N" };
                                        //下套餐
                                        Billdetails bill = new Billdetails() { FdNo = list.data.FdNo, FdQty = list.data.Count, Ai = "", FdPrice = Convert.ToInt32(list.data.FdPrice1) };
                                        orderBillNew1.listBill.Add(bill);
                                        //执行下单的存储过程
                                        preorderBll.AccountProc(orderBillNew1);
                                        OrderBillNew orderBillNew = new OrderBillNew() { CashUserId = "9978", InputUserId = "9978", RmNo = openCache.RmNo, CashType = "Z" };
                                        //将套餐详情放进购物车
                                        for (int i = 0; i < list.data.Details.Count; i++)
                                        {
                                            if (list.data.Details[i].IsFixed != 0)
                                            {
                                                Billdetails bill1 = new Billdetails() { FdNo = list.data.Details[i].FdNo, FdQty = list.data.Details[i].Count, Ai = "", FdPrice = Convert.ToInt32(list.data.Details[i].FdPrice1) };
                                                orderBillNew.listBill.Add(bill1);
                                            }

                                        }
                                        //执行下单的存储过程
                                        preorderBll.AccountProc(orderBillNew);
                                        Log.WriteLog("员工" + single.vmpc.vmuserinfo.muserinfo.UserId + "为" + openCache.RmNo + "房下单成功");
                                        #endregion

                                    }
                                    catch (Exception ex)
                                    {
                                        MessageBox.Show("获取下单项出错：" + ex.Message);
                                    }

                                }
                            }

                        }
                        #endregion

                        //2021-05-14 jjy 已提交定金弹窗提示
                        //if (book != null && book.DepositTot > 0)
                        //{
                        //    MessageBox.Show("该预约资料已支付定金，请及时绑定房间");
                        //}
                        SetGridRefresh();//刷新grid
                        SetClearInput();
                    }
                }

            }
            catch (Exception ex)
            {

                MessageBox.Show("开房过程发生异常：" + ex.Message);
            }
        }


        /// <summary>
        /// 派房操作
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRoomTo_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                bool checkResult = SetInputInfoCheck();//校验用户输入信息是否正确
                if (checkResult)
                {
                    MOpenCacheInfo openCache = GetOpenInfo();//获取开房信息
                    // WeChatUser wechatUser = uwechatSearch.GetSelectUser();//获取代订信息
                    if (openCache != null)
                    {
                        MCustInfo custinfo = GetCustInfo();//获取顾客填写信息
                        single.vmpc.vmuserinfo.SetCustRoomTo(custinfo, shopRoom, openCache, book);//派房操作
                        //2021-05-10 jjy 添加派房修改天王RmStatusNow状态（通道系统检测房间状态使用）
                        th_rms2019Bll.SetRms2009Status("W", single.vmpc.CfixedConfig.ShopId, shopRoom.RmNo);
                        #region 通知相同门店排除本次操作记录
                        if (book != null)
                            single.vmpc.client.GetSQL(new RMSModel.TCP.TcpRequest() { Conent = book.Ikey, Request = 2 });
                        #endregion
                        if (memberinfo != null)
                        {
                            memberinfo.BookIkey = openCache.Ikey;
                            RmsmemberinfoBll.SetInsertData(memberinfo);
                        }
                        // uRoom.SetChangeStatus(shopRoom.RmNo, "W");
                        SetGridRefresh();//刷新grid
                        SetClearInput();
                    }

                }
            }
            catch (Exception ex)
            {

                MessageBox.Show(ex.Message);
            }
        }
        /// <summary>
        /// 重新派房
        /// 
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRoomToRefresh_Click(object sender, RoutedEventArgs e)
        {
            if (shopRoom != null)
            {
                //uRoom.SetChangeStatus(shopRoom.RmNo, "E");
                single.vmpc.vmuserinfo.SetCustRoomToRefresh(shopRoom);
                SetGridRefresh();
            }

        }
        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRefresh_Click(object sender, RoutedEventArgs e)
        {
            SetGridRefresh();
        }
        /// <summary>
        ///房间状态点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRoomStatus_Click(object sender, RoutedEventArgs e)
        {
            WRoomAll roomall = new WRoomAll();
            roomall.ShowDialog();
        }
        /// <summary>
        /// 顾客行为按钮点击
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCustBehaInfo_Click(object sender, RoutedEventArgs e)
        {
            uCustBehaInfo.Visibility = Visibility.Visible;
            uCustBehaInfo.CustomInfo(custinfo);
        }
        /// <summary>
        /// 全部开房操作
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnAllOpen_Click(object sender, RoutedEventArgs e)
        {


            ///DSS 2021-09-10 重构逻辑实现
            //List<MRm_Rt_MArea_MShop> listRoom = uRoom.GetRoomStatus("W");
            //if (listRoom != null)
            //{
            //    //loading.Visibility = System.Windows.Visibility.Visible;

            //    //loadingTotal.Text = listRoom.Count.ToString();
            //    //loadingSurplus.Text = 0.ToString();
            //    //Thread thread = new Thread(delegate()
            //    //{
            //        /////*****将当前门店所有的派房数据拉到本地进行匹配
            //        var bookCacheList = MBookCacheInfoBll.GetBookCheckinStatus(single.vmpc.CfixedConfig.ShopId, 2);
            //        if (bookCacheList == null || bookCacheList.Count == 0) return;
            //        int number = 0;
            //        int index=0;
            //        foreach (var item in listRoom)
            //        {
            //            index++;
            //            try
            //            {
            //                var bookCache = bookCacheList.Find(i => i.Ikey == item.RoomToIkey);
            //                if (bookCache != null)
            //                {
            //                    SetOpenRoom(bookCache, item, index == listRoom.Count);
            //                    number++;

            //                    /////更新等待页面的处理数量
            //                    //this.Dispatcher.BeginInvoke(new Action(() =>
            //                    //{
            //                    //    loadingSurplus.Text = number.ToString();

            //                    //}));
            //                }
            //            }
            //            catch
            //            {

            //            }

            //        }
            //        //Thread.Sleep(1000);//停止1秒让客户端看到load页面
            //        //this.Dispatcher.BeginInvoke(new Action(() =>
            //        //{
            //                     // loading.Visibility = System.Windows.Visibility.Collapsed;
            //        //}));
            //        string tisMsg = string.Empty;
            //        if (number > 0)
            //            tisMsg = ",共开房:" + listRoom.Count + "间,成功:" + number + "间";
            //        MessageBox.Show("操作成功" + tisMsg);
            //    //});
            //    //thread.Start();

            //}
            List<MRm_Rt_MArea_MShop> listRoom = uRoom.GetRoomStatus("W");
            if (listRoom != null)
            {
                int number = 0;
                foreach (var item in listRoom)
                {

                    try
                    {
                        MBookCacheInfo bookCache = MBookCacheInfoBll.GetBookKey(item.RoomToIkey);//获取预约信息
                        SetOpenRoom(bookCache, item);
                        number++;
                    }
                    catch
                    {

                    }

                }
                MessageBox.Show("操作成功" + (number > 0 ? (",有" + number + "条失败记录") : string.Empty));

            }
        }

        /// <summary>
        /// 开房操作
        /// </summary>
        public void SetOpenRoom(MBookCacheInfo bookCache, MRm_Rt_MArea_MShop mshop, bool isRefreshCou = true)
        {
            if (bookCache != null)
            {
                MOpenCacheInfo openCache = new MOpenCacheInfo()
                {
                    CustKey = bookCache.CustKey,
                    Beg_Key = bookCache.Beg_Key,
                    Beg_Name = bookCache.Beg_Name,
                    BookDateTime = bookCache.BookDateTime,
                    BookMemory = bookCache.BookMemory,
                    BookNo = bookCache.BookNo,
                    BookShopId = bookCache.BookShopId,
                    BookStatus = bookCache.BookStatus,
                    BookUserId = bookCache.BookUserId,
                    BookUserName = bookCache.BookUserName,
                    CheckinStatus = bookCache.CheckinStatus,
                    ComeDate = bookCache.ComeDate,
                    ComeTime = bookCache.ComeTime,
                    CtName = bookCache.CtName,
                    CtNo = bookCache.CtNo,
                    CustName = bookCache.CustName,
                    CustTel = bookCache.CustTel,
                    End_Key = bookCache.End_Key,
                    End_Name = bookCache.End_Name,
                    Numbers = bookCache.Numbers,
                    OpenMemory = string.Empty,
                    PtName = bookCache.PtName,
                    PtNo = bookCache.PtNo,
                    RmNo = bookCache.RmNo,
                    RtName = bookCache.RtName,
                    RtNo = bookCache.RtNo,
                    Val1 = bookCache.Val1,
                    OrderUserID = bookCache.OrderUserID,
                    OrderUserName = bookCache.OrderUserName,
                    DemandNumber = bookCache.DemandNumber//20

                };
                MCustInfo custinfo = MCustInfoBll.GetMCustInfo(bookCache.CustKey);

                //uRoom.SetChangeStatus(item.RmNo, "U");
                single.vmpc.vmuserinfo.SetCustOpenRoom(custinfo, mshop, openCache, bookCache, isRefreshCou);
            }
            else
            {
                MessageBox.Show("开房失败,‘" + mshop.RmNo + "’房找不到预约信息。");
            }
        }
        /// <summary>
        /// 自定义小票打印格式
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>

        private void btnPrintFormat_Click(object sender, RoutedEventArgs e)
        {
            //uCanvasPrintFormat.Visibility = Visibility.Visible;
        }
        /// <summary>
        /// datagrid 行初始化
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void dgBook_LoadingRow(object sender, DataGridRowEventArgs e)
        {
            //DataGridRow dataGridRow = e.Row;
            //MBookCacheInfo dataRow = e.Row.Item as MBookCacheInfo;
            // dataRow.SetGridColor();
            //if (dataRow.CheckinStatus == 1)
            //{
            //    dataGridRow.Background = Brushes.Gray;
            //}
            //else if (dataRow.Val1 == 1)
            //{
            //    dataGridRow.Background = Brushes.Lime;
            //}
            //2020-12-18 jjy 添加已预下单背景色显示浅粉色
            bookcacheinfo_preorder dataRow = e.Row.Item as bookcacheinfo_preorder;
            dataRow.SetGridColor_New();

        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="MenuItemName">点击内容</param>
        /// <param name="shop">房间信息</param>
        private void URoom_menuItemClick(string MenuItemName, MRm_Rt_MArea_MShop room)
        {
            MOpenCacheInfo openCache = null;
            switch (MenuItemName)
            {
                case "开房":
                    try
                    {
                        MBookCacheInfo bookCache = MBookCacheInfoBll.GetBookKey(room.RoomToIkey);//获取预约信息
                        SetOpenRoom(bookCache, room);
                    }
                    catch (Exception ex)
                    {

                        MessageBox.Show("操作失败" + ex.Message);
                    }
                    break;
                case "打印":
                    openCache = openRoom.DataContext as MOpenCacheInfo;
                    if (openCache != null)
                    {
                        BookPrint.SetBookBillPrint(openCache);//小票打印
                        BookPrint.SetBookBillTopPrint(openCache);
                    }
                    break;
                case "清房":
                    th_rms2019Bll.SetClearingToEmptyRoom(room.RmNo);
                    break;
                case "全部清房":
                    List<MRm_Rt_MArea_MShop> roomlist = uRoom.GetRoom_D();
                    if (roomlist != null)
                    {
                        foreach (var item in roomlist)
                        {
                            th_rms2019Bll.SetClearingToEmptyRoom(item.RmNo);
                        }
                    }
                    break;
                case "微信":
                    // uRoom.SetChangeStatus(room.RmNo, "V");
                    MRmInfoBll.GetMRmInfo_Udp(single.vmpc.CfixedConfig.ShopId, room.RmNo, "V", "", "");
                    // th_rms2019Bll.SetRms2009Status("V", single.vmpc.CfixedConfig.ShopId, room.RmNo);
                    break;
                case "取消微信":
                    MRmInfoBll.GetMRmInfo_Udp(single.vmpc.CfixedConfig.ShopId, room.RmNo, "E", "", "");
                    //  th_rms2019Bll.SetRms2009Status("", single.vmpc.CfixedConfig.ShopId, room.RmNo);
                    break;
                case "转房":
                    roomchange.SetData(room.RmNo);
                    roomchange.Visibility = Visibility.Visible;
                    break;
                case "坏房":

                    gridBad.Tag = room;
                    gridBad.Visibility = Visibility.Visible;
                    room.RmPresetState = "B";
                    tbBadTitle.Text = MenuItemName;
                    tbBadText.Focus();

                    break;
                case "慢带":
                    gridBad.Tag = room;
                    gridBad.Visibility = Visibility.Visible;
                    room.RmPresetState = "L";
                    tbBadTitle.Text = MenuItemName;
                    tbBadText.Focus();

                    //MRmInfoBll.GetMRmInfo_Udp(single.vmpc.CfixedConfig.ShopId, room.RmNo, "L", "", "");
                    //th_rms2019Bll.SetRms2009Status("L", single.vmpc.CfixedConfig.ShopId, room.RmNo);

                    break;
                case "留房":
                    gridBad.Tag = room;
                    gridBad.Visibility = Visibility.Visible;
                    room.RmPresetState = "R";
                    tbBadTitle.Text = MenuItemName;
                    tbBadText.Focus();

                    //MRmInfoBll.GetMRmInfo_Udp(single.vmpc.CfixedConfig.ShopId, room.RmNo, "R", "", "");
                    //th_rms2019Bll.SetRms2009Status("R", single.vmpc.CfixedConfig.ShopId, room.RmNo);
                    break;
                case "客离":
                    if (room.RmsStatus == "B")
                    {
                        th_rms2019Bll.Rm_SetBadOff(room.RmNo);
                    }
                    else
                    {
                        MRmInfoBll.GetMRmInfo_Udp(single.vmpc.CfixedConfig.ShopId, room.RmNo, "E", "", "");
                        th_rms2019Bll.SetRms2009Status(string.Empty, single.vmpc.CfixedConfig.ShopId, room.RmNo);

                    }
                    break;

                case "打折":
                    openCache = openRoom.DataContext as MOpenCacheInfo;
                    if (openCache != null)
                    {
                        openCache.Val1 = 1;
                        OpenCacheInfoBll.SetUpdData(openCache);
                    }
                    // MRmInfoBll.GetMRmInfo_Udp(single.vmpc.CfixedConfig.ShopId, room.RmNo, 1);
                    break;
                case "取消打折":
                    openCache = openRoom.DataContext as MOpenCacheInfo;
                    if (openCache != null)
                    {
                        openCache.Val1 = 0;
                        OpenCacheInfoBll.SetUpdData(openCache);
                    }
                    // MRmInfoBll.GetMRmInfo_Udp(single.vmpc.CfixedConfig.ShopId, room.RmNo, 0);
                    break;
                case "取消派房":
                    if (shopRoom != null)
                    {
                        single.vmpc.vmuserinfo.SetCustRoomToRefresh(shopRoom);
                        SetGridRefresh();
                    }
                    break;
                case "直落":
                    if (shopRoom != null)
                    {
                        addtims.SetData(shopRoom);
                    }
                    break;
                case "加钟重开":
                    if (shopRoom != null)
                    {
                        addtims.SetDate(shopRoom, true);
                    }
                    break;
                case "顾客生日":
                    try
                    {
                        openCache = openRoom.DataContext as MOpenCacheInfo;
                        if (openCache != null)
                        {
                            openCache.IsBirthday = true;
                            OpenCacheInfoBll.SetUpdData(openCache);
                            ConinfoBll.SetRoomModel(room.RmNo, openCache.InvNo + "、1");

                        }
                    }
                    catch (Exception ex)
                    {
                    }
                    break;
                case "取消顾客生日":
                    try
                    {
                        openCache = openRoom.DataContext as MOpenCacheInfo;
                        if (openCache != null)
                        {
                            openCache.IsBirthday = false;
                            OpenCacheInfoBll.SetUpdData(openCache);
                            ConinfoBll.SetRoomModel(room.RmNo, string.Empty);

                        }
                    }
                    catch
                    {
                    }
                    break;
            }
        }
        /// <summary>
        /// 时段选择
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tbShopTime_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (tbShopTime.SelectedItem != null)
            {
                if (tbShopTime.SelectedItem is MShopTimeInfoJoin)
                {
                    shoptime = tbShopTime.SelectedItem as MShopTimeInfoJoin;
                    cbConType.ItemsSource = null;
                    cbConType.ItemsSource = single.vmpc.vmConTypeInfo.GetConType_Open(shopid, shoptime, shoptime);
                    cbConType.SelectedIndex = 0;
                    SetGridRefresh();
                }
                else
                {
                    MessageBox.Show("获取时段过程中，类型转换不一致!");
                }
            }
        }



        #region 坏房逻辑处理

        private void BtnNo_Click(object sender, RoutedEventArgs e)
        {
            tbBadText.Text = "";
            gridBad.Visibility = Visibility.Collapsed;
        }

        private void btnOk_Click(object sender, RoutedEventArgs e)
        {
            try
            {

                MRm_Rt_MArea_MShop mroom = gridBad.Tag as MRm_Rt_MArea_MShop;
                if (mroom != null && !string.IsNullOrEmpty(mroom.RmPresetState))
                {
                    if (mroom.RmPresetState == "B")
                    {
                        gridBad.Visibility = Visibility.Collapsed;
                        th_rms2019Bll.Rm_SetBadOn(mroom.RmNo, tbBadText.Text);
                        MRmInfoBll.SetMRmInfo_Remark(single.vmpc.CfixedConfig.ShopId, mroom.RmNo, tbBadText.Text);
                    }
                    else
                    {
                        MRmInfoBll.GetMRmInfo_Udp(single.vmpc.CfixedConfig.ShopId, mroom.RmNo, mroom.RmPresetState, "", "", tbBadText.Text);
                        th_rms2019Bll.SetRms2009Status(mroom.RmPresetState, single.vmpc.CfixedConfig.ShopId, mroom.RmNo);
                    }
                }
                BtnNo_Click(null, null);
            }
            catch (Exception ex)
            {


            }


        }
        #endregion

        private void gridCustBehaInfo_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            uCustBehaInfo.Visibility = Visibility.Collapsed;

        }
        /// <summary>
        /// 顾客资料修改点击
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnCustUpdate_Click(object sender, RoutedEventArgs e)
        {

            MOpenCacheInfo openCacheInfo = openRoom.DataContext as MOpenCacheInfo;
            if (openCacheInfo != null)
            {
                updcust.SetData(openCacheInfo);
                updcust.Visibility = Visibility.Visible;

            }
        }
        /// <summary>
        /// 数据回绑按钮
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCustFill_Click(object sender, RoutedEventArgs e)
        {
            if (shopRoom != null)
            {
                refill.SetData();
                refill.Visibility = Visibility.Visible;
            }

        }
        string tbOpenMemoryconent = string.Empty;
        /// <summary>
        ///当房选择变更
        ///         1:修改开房备注信息
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void cbRoomType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {

            if (custinfo != null && shopRoom != null)
            {

                MRt_MShop rtinfo = cbRoomType.SelectedItem as MRt_MShop;
                if (rtinfo != null && rtinfo.RtNo != shopRoom.RtNo)
                {
                    tbOpenMemoryconent = rtinfo.RtName + "收费";
                    tbOpenMemory.Text = tbOpenMemoryconent;

                }
                else if (tbOpenMemory.Text == tbOpenMemoryconent)
                {
                    tbOpenMemory.Text = string.Empty;

                }



            }
        }

        /// <summary>
        /// 电话号码信息变更
        /// </summary>
        /// <param name="custinfo"></param>
        private void tbTel_TextChanged_1(MCustInfo custinfo)
        {

            if (custinfo != null)
            {
                this.custinfo = custinfo;
                if (book == null)
                {//没有预约号，则绑定到达日期绑定系统当前时间
                    dtpComtime.SetTimeDate(DateTime.Now.ToString("HH:mm:ss"));
                }
                SetCustInfo(custinfo);//绑定顾客信息
                GetGridBookInfo(custinfo.CustTel);
                tbTel.IsEnabled = true;
                tbTel.Focus();
                if (shoptime != null)
                {

                    MRt_MShop rtinfo = cbRoomType.SelectedItem as MRt_MShop;
                    if (rtinfo != null)
                    {
                        CustBehaviorManage.YearNightCou(custinfo, shoptime.TimeNo, rtinfo.RtName);//11
                    }
                }
            }
            else
            {
                single.vmpc.vmCustInfo.mCustInfoCall = null;

                SetClearInput(false);
                dgBook.SelectedIndex = -1;
                tbName.Text = string.Empty;
                SetScreenRtCancel();//取消推荐房型
                uwechatSearch.GetSelectUser();
                cbDiscount.IsChecked = false;
            }
        }

        private void btnAllClear_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var res = MessageBox.Show("确认执行全部清房操作?", "提示", MessageBoxButton.OKCancel, MessageBoxImage.Question);
                if (res == MessageBoxResult.OK)
                {
                    th_rms2019Bll.SetRoomClear(null);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);

            }
        }

        private void btnClear_Click(object sender, RoutedEventArgs e)
        {
            if (shopRoom != null)
            {
                try
                {
                    th_rms2019Bll.SetRoomClear(shopRoom.RmNo);
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);

                }
            }

        }

        //private void cbPrint_Click(object sender, RoutedEventArgs e)
        //{
        //    VMPC.IsPrint = cbPrint.IsChecked.Value;
        //    if (VMPC.IsPrint == true)
        //        MessageBox.Show("打印已开启");
        //    else
        //        MessageBox.Show("打印已关闭");


        // }
        /// <summary>
        /// 刷新按钮点击
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void imgRefresh_MouseUp(object sender, MouseButtonEventArgs e)
        {
            SetGridRefresh();
            SetClearInput();
        }


        private void imgClear_MouseUp(object sender, MouseButtonEventArgs e)
        {
            SetClearInput();
        }


        /// <summary>
        /// 微信取房
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnWechatRoom_Click(object sender, RoutedEventArgs e)
        {
            WOpen_WechatInfo openwechat = new WOpen_WechatInfo();
            openwechat.ShowDialog();
        }



        private void isDbfoodSynchro_Click(object sender, RoutedEventArgs e)
        {
            if (isDbfoodSynchro.IsChecked == true)
            {
                MessageBoxResult result = MessageBox.Show("此功能为应急方案，确定要在本设备开启天王同步功能吗，非必要情况下不推荐使用！", "提示", MessageBoxButton.OKCancel, MessageBoxImage.Information);
                if (result == MessageBoxResult.OK)
                {
                    SingleRun.isDbfoodSynchro = true;
                }
                else
                {
                    isDbfoodSynchro.IsChecked = false;
                    SingleRun.isDbfoodSynchro = false;
                }
            }
            else { SingleRun.isDbfoodSynchro = false; }
        }

        private void usmsinfo_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            btnSms_Click(null, null);
        }




        /// <summary>
        /// 确定回绑按钮(2020-12-25 jjy 添加)
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Btn_Confim_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(ikey) == true)
            {
                MessageBox.Show("您尚未选择回绑的用户信息");
                return;
            }
            try
            {
                int result = OpenCacheInfoBll.ReFillOpenData(shopid, shopRoom.RmNo, ikey);
                if (result > 0)
                {
                    Log.WriteLog(single.vmpc.CfixedConfig.shopInfo.ShopName + "   " + shopRoom.RmNo + "房开房数据回绑成功！");
                    shopRoom.RoomToIkey = ikey;
                    CheckedChangBinging(shopRoom);
                    openRoom.btnCustFill.Visibility = Visibility.Collapsed;
                    MessageBox.Show("回绑成功！");
                    refill.Visibility = Visibility.Collapsed;
                }
                else
                    MessageBox.Show("回绑失败");
            }
            catch (Exception ex)
            {
                MessageBox.Show("过程发生异常：" + ex.Message);
            }

        }
        /// <summary>
        /// 搜索按钮点击
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSearch_Click(object sender, RoutedEventArgs e)
        {
            SetSearch();
        }
        /// <summary>
        /// 检查键盘输入
        ///    当触发回车按钮则触发搜索
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tbPhone_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                btnSearch_Click(null, null);
            }
        }

        /// <summary>
        /// 查询数据
        /// </summary>
        public void SetSearch()
        {
            string tel = refill.tbPhone.Text;
            if (string.IsNullOrEmpty(tel) == false)
            {
                try
                {
                    MOpenCacheInfo openCache = OpenCacheInfoBll.GetOpenByCustTelOrBookNo(shopid, DateTime.Now.ToString("yyyyMMdd"), tel);
                    if (openCache != null)
                    {
                        refill.openroom.DataContext = openCache;
                        ikey = openCache.Ikey;
                    }
                    else
                    {
                        try
                        {
                            List<bookcacheinfo_preorder> bookList = MBookCacheInfoBll.GetBookTelAndBookNo(tel, DateTime.Now.ToString("yyyyMMdd"));
                            if (bookList.Count > 0)
                            {
                                refill.openroom.DataContext = bookList;
                                ikey = bookList[0].Ikey;
                            }
                            else
                            {
                                ikey = "";
                                refill.openroom.DataContext = null;
                                MessageBox.Show("查询不到该会员的预约开房信息");
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show("预约资料获取失败：" + ex.Message);
                        }

                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show("开房资料获取失败：" + ex.Message);
                }
            }
        }



        private void btnTipClose(object sender, RoutedEventArgs e)
        {
            var res = MessageBox.Show("确定关闭加载窗口？", "提示", MessageBoxButton.OKCancel, MessageBoxImage.None);
            if (res == MessageBoxResult.OK)
            {
                loading.Visibility = System.Windows.Visibility.Collapsed;
            }
        }

        private void btnExtend_Click(object sender, RoutedEventArgs e)
        {
            SingleRun.GetSingle().GetWebsite().Open();
        }

        private void btnMore_Click(object sender, RoutedEventArgs e)
        {
            //Window win = new Window();

            //AppBook BOOK = new AppBook();
            //win.Content = BOOK;

            //win.ShowDialog();

            //return;
            popupFunction.IsOpen = true;

        }



        private void btnMore_MouseLeave(object sender, MouseEventArgs e)
        {
            popupFunction.IsOpen = false;

        }

        private void btnMore_MouseEnter(object sender, MouseEventArgs e)
        {
            //popupFunction.IsOpen = true;

        }

        private void btnCloseRoom_Click(object sender, RoutedEventArgs e)
        {
            WRoomManage_New wroom = new WRoomManage_New();
            wroom.ShowDialog();
        }
        private void btnReset_Click(object sender, RoutedEventArgs e)
        {
            RmLock_utils.Lock_clear();
        }
        /// <summary>
        /// /营业报表
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnbusinessReport_Click(object sender, RoutedEventArgs e)
        {
            WRoomsReport Wroom = new WRoomsReport();
            Wroom.ShowDialog();

        }

        private void tbBadText_KeyUp(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                btnOk_Click(null, null);
            }
            else if (e.Key == Key.Escape)
            {
                BtnNo_Click(null, null);
            }
        }
        private void Button_Click(object sender, RoutedEventArgs e)
        {
            AccessBar bar = new AccessBar();
            bar.WindowStartupLocation = System.Windows.WindowStartupLocation.CenterScreen;
            bar.ShowDialog();
        }

        private void CheckBox_Checked(object sender, RoutedEventArgs e)
        {
            uRoom.SetUiModel(true);
        }

        private void CheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            uRoom.SetUiModel(false);
        }

        private void tbSearch_TextChanged(object sender, TextChangedEventArgs e)
        {

        }
    }
}
