using System;
using System.Collections.Generic;
using System.Linq;
using RMSModel.RMS;
using RMSModel.RMS.Api;

namespace RMS2018.TestData
{
    /// <summary>
    /// 模拟数据服务 - 用于本地测试环境
    /// </summary>
    public static class MockDataService
    {
        private static bool _isTestMode = false;
        private static List<MRm_Rt_MArea_MShop> _mockRoomData;
        private static List<MOpenCacheInfo> _mockOpenCacheData;
        private static List<RmExchange> _mockRmExchangeData;

        /// <summary>
        /// 启用测试模式
        /// </summary>
        public static void EnableTestMode()
        {
            _isTestMode = true;
            InitializeMockData();
        }

        /// <summary>
        /// 禁用测试模式
        /// </summary>
        public static void DisableTestMode()
        {
            _isTestMode = false;
            _mockRoomData = null;
            _mockOpenCacheData = null;
            _mockRmExchangeData = null;
        }

        /// <summary>
        /// 检查是否为测试模式
        /// </summary>
        public static bool IsTestMode => _isTestMode;

        /// <summary>
        /// 初始化模拟数据
        /// </summary>
        private static void InitializeMockData()
        {
            CreateMockRoomData();
            CreateMockOpenCacheData();
            CreateMockRmExchangeData();
        }

        /// <summary>
        /// 创建模拟房间数据
        /// </summary>
        private static void CreateMockRoomData()
        {
            _mockRoomData = new List<MRm_Rt_MArea_MShop>();

            // 创建不同房型的房间
            var roomTypes = new[]
            {
                new { RtNo = "001", RtName = "标准大床房", Price = 150 },
                new { RtNo = "002", RtName = "豪华大床房", Price = 200 },
                new { RtNo = "003", RtName = "总统套房", Price = 500 },
                new { RtNo = "004", RtName = "商务套房", Price = 300 }
            };

            var roomStatuses = new[] { "E", "U", "C", "W", "F" }; // E:空闲, U:占用, C:清扫, W:维修, F:预结

            int roomIndex = 0;
            foreach (var roomType in roomTypes)
            {
                for (int floor = 1; floor <= 3; floor++)
                {
                    for (int roomNum = 1; roomNum <= 8; roomNum++)
                    {
                        string rmNo = $"{floor}{roomNum:00}{roomType.RtNo.Substring(2, 1)}";
                        string status = roomStatuses[roomIndex % roomStatuses.Length];
                        
                        var room = new MRm_Rt_MArea_MShop
                        {
                            ShopId = 1, // 测试门店ID
                            RmNo = rmNo,
                            RtNo = roomType.RtNo,
                            RtName = roomType.RtName,
                            RmsStatus = status,
                            AreaId = floor,
                            AreaName = $"{floor}楼",
                            IsDisplay = true,
                            RoomToIkey = status == "E" ? "" : Guid.NewGuid().ToString(),
                            BookNo = status == "E" ? "" : $"BK{DateTime.Now:yyyyMMdd}{roomIndex:000}",
                            CustTel = status == "E" ? "" : $"138{roomIndex:0000}0000",
                            InvNo = status == "E" ? "" : $"INV{DateTime.Now:yyyyMMdd}{roomIndex:000}"
                        };

                        _mockRoomData.Add(room);
                        roomIndex++;
                    }
                }
            }
        }

        /// <summary>
        /// 创建模拟开房缓存数据
        /// </summary>
        private static void CreateMockOpenCacheData()
        {
            _mockOpenCacheData = new List<MOpenCacheInfo>();

            var occupiedRooms = _mockRoomData.Where(r => r.RmsStatus != "E").ToList();
            
            foreach (var room in occupiedRooms)
            {
                var openCache = new MOpenCacheInfo
                {
                    Ikey = room.RoomToIkey,
                    ShopId = room.ShopId,
                    RmNo = room.RmNo,
                    RtNo = room.RtNo,
                    RtName = room.RtName,
                    CustName = $"测试客户{room.RmNo}",
                    CustTel = room.CustTel,
                    BookNo = room.BookNo,
                    InvNo = room.InvNo,
                    ComeDate = DateTime.Now.ToString("yyyyMMdd"),
                    ComeTime = DateTime.Now.AddHours(-2).ToString("HHmm"),
                    Numbers = 2,
                    BookStatus = "1",
                    CheckinStatus = "1"
                };

                _mockOpenCacheData.Add(openCache);
            }
        }

        /// <summary>
        /// 创建模拟转房记录数据
        /// </summary>
        private static void CreateMockRmExchangeData()
        {
            _mockRmExchangeData = new List<RmExchange>();

            // 创建一些历史转房记录
            for (int i = 0; i < 5; i++)
            {
                var exchange = new RmExchange
                {
                    ExchangeDate = DateTime.Now.AddDays(-i),
                    FromRmNo = $"10{i + 1}1",
                    ToRmNo = $"20{i + 1}1",
                    UserName = "测试用户",
                    ShopId = 1,
                    IsExchange = true
                };

                _mockRmExchangeData.Add(exchange);
            }
        }

        /// <summary>
        /// 获取模拟房间数据
        /// </summary>
        public static List<MRm_Rt_MArea_MShop> GetMockRoomData(int shopId)
        {
            if (!_isTestMode) return new List<MRm_Rt_MArea_MShop>();
            return _mockRoomData?.Where(r => r.ShopId == shopId).ToList() ?? new List<MRm_Rt_MArea_MShop>();
        }

        /// <summary>
        /// 获取模拟开房缓存数据
        /// </summary>
        public static List<MOpenCacheInfo> GetMockOpenCacheData(int shopId)
        {
            if (!_isTestMode) return new List<MOpenCacheInfo>();
            return _mockOpenCacheData?.Where(r => r.ShopId == shopId).ToList() ?? new List<MOpenCacheInfo>();
        }

        /// <summary>
        /// 获取模拟转房记录数据
        /// </summary>
        public static List<RmExchange> GetMockRmExchangeData(int shopId)
        {
            if (!_isTestMode) return new List<RmExchange>();
            return _mockRmExchangeData?.Where(r => r.ShopId == shopId).ToList() ?? new List<RmExchange>();
        }

        /// <summary>
        /// 模拟转房操作
        /// </summary>
        public static void MockRoomTransfer(int shopId, string fromRmNo, string toRmNo, string userName, bool keepRate)
        {
            if (!_isTestMode) return;

            // 更新房间状态
            var fromRoom = _mockRoomData.FirstOrDefault(r => r.ShopId == shopId && r.RmNo == fromRmNo);
            var toRoom = _mockRoomData.FirstOrDefault(r => r.ShopId == shopId && r.RmNo == toRmNo);

            if (fromRoom != null && toRoom != null)
            {
                // 交换房间信息
                var tempStatus = fromRoom.RmsStatus;
                var tempRoomToIkey = fromRoom.RoomToIkey;
                var tempBookNo = fromRoom.BookNo;
                var tempCustTel = fromRoom.CustTel;
                var tempInvNo = fromRoom.InvNo;

                fromRoom.RmsStatus = "E";
                fromRoom.RoomToIkey = "";
                fromRoom.BookNo = "";
                fromRoom.CustTel = "";
                fromRoom.InvNo = "";

                toRoom.RmsStatus = tempStatus;
                toRoom.RoomToIkey = tempRoomToIkey;
                toRoom.BookNo = tempBookNo;
                toRoom.CustTel = tempCustTel;
                toRoom.InvNo = tempInvNo;

                // 更新开房缓存数据
                var openCache = _mockOpenCacheData.FirstOrDefault(o => o.Ikey == tempRoomToIkey);
                if (openCache != null)
                {
                    openCache.FromRmNo = fromRmNo;
                    openCache.RmNo = toRmNo;
                    
                    // 如果不保持原房型，则更新房型信息
                    if (!keepRate)
                    {
                        openCache.RtNo = toRoom.RtNo;
                        openCache.RtName = toRoom.RtName;
                    }
                }

                // 添加转房记录
                var exchange = new RmExchange
                {
                    ExchangeDate = DateTime.Now,
                    FromRmNo = fromRmNo,
                    ToRmNo = toRmNo,
                    UserName = userName,
                    ShopId = shopId,
                    IsExchange = true
                };

                _mockRmExchangeData.Add(exchange);
            }
        }

        /// <summary>
        /// 重置模拟数据
        /// </summary>
        public static void ResetMockData()
        {
            if (_isTestMode)
            {
                InitializeMockData();
            }
        }
    }
}
