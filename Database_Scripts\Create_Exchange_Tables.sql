-- =============================================
-- 创建转房相关的数据库表
-- Author: System
-- Create date: 2025-01-16
-- Description: 创建转房功能所需的表结构
-- =============================================

USE RMS2019
GO

-- 1. 创建转房记录表 (RmExchange)
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RmExchange]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[RmExchange](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [FromRmNo] [varchar](50) NOT NULL,
        [ToRmNo] [varchar](50) NOT NULL,
        [UserName] [varchar](100) NULL,
        [ExchangeDate] [datetime] NOT NULL,
        [ShopId] [int] NOT NULL,
        [Reason] [varchar](500) NULL,
        [BillingType] [int] NULL, -- 0=免费升级, 1=付费升级
        [OriginalRtNo] [varchar](50) NULL,
        [NewRtNo] [varchar](50) NULL,
        [PriceDifference] [decimal](10,2) NULL,
        [CreateTime] [datetime] DEFAULT GETDATE(),
        CONSTRAINT [PK_RmExchange] PRIMARY KEY CLUSTERED ([id] ASC)
    )
    
    PRINT 'RmExchange 表创建成功！'
END
ELSE
BEGIN
    PRINT 'RmExchange 表已存在。'
END

-- 2. 创建预转房等待表 (ExchangeWait)
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[ExchangeWait]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[ExchangeWait](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [FromRmNo] [varchar](50) NOT NULL,
        [ToRmNo] [varchar](50) NOT NULL,
        [UserName] [varchar](100) NULL,
        [ShopId] [int] NOT NULL,
        [Reason] [varchar](500) NULL,
        [Status] [int] DEFAULT 0, -- 0=等待, 1=已处理, 2=已取消
        [CreateTime] [datetime] DEFAULT GETDATE(),
        [ProcessTime] [datetime] NULL,
        CONSTRAINT [PK_ExchangeWait] PRIMARY KEY CLUSTERED ([id] ASC)
    )
    
    PRINT 'ExchangeWait 表创建成功！'
END
ELSE
BEGIN
    PRINT 'ExchangeWait 表已存在。'
END

-- 3. 创建转房历史表 (RoomChangeHistory) - 用于我们的计费功能
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[RoomChangeHistory]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[RoomChangeHistory](
        [Id] [int] IDENTITY(1,1) NOT NULL,
        [ShopId] [int] NOT NULL,
        [FromRoomNo] [varchar](50) NOT NULL,
        [ToRoomNo] [varchar](50) NOT NULL,
        [FromRoomType] [varchar](50) NOT NULL,
        [ToRoomType] [varchar](50) NOT NULL,
        [BillingChoice] [varchar](20) NOT NULL, -- 'FreeUpgrade' 或 'PaidUpgrade'
        [OriginalPrice] [decimal](10,2) NULL,
        [NewPrice] [decimal](10,2) NULL,
        [PriceDifference] [decimal](10,2) NULL,
        [CustomerName] [varchar](100) NULL,
        [CustomerPhone] [varchar](50) NULL,
        [ChangeReason] [varchar](500) NULL,
        [OperatorUserId] [varchar](50) NOT NULL,
        [OperatorUserName] [varchar](100) NOT NULL,
        [ChangeDate] [date] NOT NULL,
        [ChangeTime] [datetime] NOT NULL,
        [BookingNo] [varchar](100) NULL,
        [InvoiceNo] [varchar](100) NULL,
        [Remarks] [varchar](1000) NULL,
        CONSTRAINT [PK_RoomChangeHistory] PRIMARY KEY CLUSTERED ([Id] ASC)
    )
    
    -- 创建索引
    CREATE INDEX [IX_RoomChangeHistory_ShopId_ChangeDate] ON [dbo].[RoomChangeHistory] ([ShopId], [ChangeDate])
    CREATE INDEX [IX_RoomChangeHistory_FromRoomNo] ON [dbo].[RoomChangeHistory] ([FromRoomNo])
    CREATE INDEX [IX_RoomChangeHistory_ToRoomNo] ON [dbo].[RoomChangeHistory] ([ToRoomNo])
    
    PRINT 'RoomChangeHistory 表创建成功！'
END
ELSE
BEGIN
    PRINT 'RoomChangeHistory 表已存在。'
END

-- 4. 插入一些测试数据
IF NOT EXISTS (SELECT 1 FROM RmExchange WHERE ShopId = 1)
BEGIN
    INSERT INTO RmExchange (FromRmNo, ToRmNo, UserName, ExchangeDate, ShopId, Reason, BillingType, OriginalRtNo, NewRtNo)
    VALUES 
    ('101', '201', '测试用户1', DATEADD(day, -2, GETDATE()), 1, '客户要求升级', 1, '001', '002'),
    ('102', '301', '测试用户2', DATEADD(day, -5, GETDATE()), 1, '房间设施问题', 0, '001', '003'),
    ('103', '202', '测试用户3', DATEADD(day, -1, GETDATE()), 1, '免费升级', 0, '001', '002')
    
    PRINT '测试转房记录插入成功！'
END

IF NOT EXISTS (SELECT 1 FROM ExchangeWait WHERE ShopId = 1)
BEGIN
    INSERT INTO ExchangeWait (FromRmNo, ToRmNo, UserName, ShopId, Reason, Status)
    VALUES 
    ('104', '302', '测试用户4', 1, '等待转房', 0),
    ('105', '203', '测试用户5', 1, '预约转房', 0)
    
    PRINT '测试预转房记录插入成功！'
END

-- 5. 验证表创建和数据插入
SELECT 'RmExchange' as TableName, COUNT(*) as RecordCount FROM RmExchange WHERE ShopId = 1
UNION ALL
SELECT 'ExchangeWait' as TableName, COUNT(*) as RecordCount FROM ExchangeWait WHERE ShopId = 1
UNION ALL
SELECT 'RoomChangeHistory' as TableName, COUNT(*) as RecordCount FROM RoomChangeHistory WHERE ShopId = 1

PRINT '转房相关表结构创建完成！'
