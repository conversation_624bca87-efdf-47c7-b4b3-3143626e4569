using System;
using System.Windows;
using RMSUtils;
using RMSUtils.RMS;
using RMSBLL.DbFood;

namespace RMS2018
{
    /// <summary>
    /// 测试启动器 - 用于绕过正常的启动流程，直接进入测试模式
    /// </summary>
    public class TestLauncher
    {
        [STAThread]
        public static void Main(string[] args)
        {
            try
            {
                // 创建应用程序实例
                RMS2018.App app = new RMS2018.App();
                app.InitializeComponent();

                // 加载XAML资源
                System.Uri resourceLocater = new System.Uri("/RMS2018;component/app.xaml", System.UriKind.Relative);
                System.Windows.Application.LoadComponent(app, resourceLocater);
                
                // 设置测试环境
                SetupTestEnvironment();
                
                // 直接启动登录窗口
                Window window = new WLogin();
                
                // 运行应用程序
                app.Run(window);
            }
            catch (Exception ex)
            {
                MessageBox.Show("测试启动器发生异常：" + ex.Message + "\n" + ex.StackTrace, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        
        /// <summary>
        /// 设置测试环境
        /// </summary>
        private static void SetupTestEnvironment()
        {
            try
            {
                // 设置默认的ShopId
                string shopId = System.Configuration.ConfigurationManager.AppSettings["shopid"] ?? "1";
                Regedit.SetVal("ShopId", shopId);
                Regedit.SetVal("Activation_rms", "yes");

                // 设置数据库连接
                th_rms2019Bll.SetConnection("192.168.2.14");

                // 预初始化CFixedConfig以避免NullReference错误
                try
                {
                    RMSModel.Config.CFixedConfig.shopInfoStatic = new RMSModel.RMS.MShopInfo()
                    {
                        ShopId = int.Parse(shopId),
                        ShopName = $"测试门店{shopId}"
                    };
                }
                catch (Exception configEx)
                {
                    System.Diagnostics.Debug.WriteLine($"配置初始化警告: {configEx.Message}");
                }

                MessageBox.Show(
                    "测试环境已配置：\n" +
                    "• 数据库服务器：192.168.2.14\n" +
                    "• 数据库名称：RMS2019\n" +
                    "• 门店ID：" + shopId + "\n" +
                    "• 自动登录：已启用\n" +
                    "• 配置预初始化：完成\n\n" +
                    "系统将自动登录并进入主界面。",
                    "测试模式",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information
                );
            }
            catch (Exception ex)
            {
                MessageBox.Show("设置测试环境失败：" + ex.Message, "警告", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }
    }
}
