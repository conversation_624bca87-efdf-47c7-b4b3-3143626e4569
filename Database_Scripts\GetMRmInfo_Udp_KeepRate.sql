-- =============================================
-- 创建新的存储过程：GetMRmInfo_Udp_KeepRate
-- 功能：转房时只更新物理房号，保持原计费房型不变（免费升级场景）
-- 作者：系统自动生成
-- 创建日期：2025-07-15
-- =============================================

-- 注意：这个脚本需要数据库管理员执行
-- 该存储过程是基于原有的 GetMRmInfo_Udp 存储过程修改而来
-- 主要区别是在更新 OpenCacheInfo 表时，只更新 RmNo 和 FromRmNo，
-- 不更新 RtNo 和 RtName，从而保持原始的计费房型

CREATE PROCEDURE [dbo].[GetMRmInfo_Udp_KeepRate]
    @ShopId INT,
    @RoomToNo NVARCHAR(50),
    @RoomFromNo NVARCHAR(50),
    @UserName NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- 获取转出房间的信息
        DECLARE @FromRoomInfo TABLE (
            RoomToIkey NVARCHAR(50),
            RtUp NVARCHAR(50),
            BillTot INT,
            BookNo NVARCHAR(50),
            CustTel NVARCHAR(50),
            IsDiscount BIT,
            RmsStatus NVARCHAR(10)
        );
        
        INSERT INTO @FromRoomInfo
        SELECT RoomToIkey, RtUp, BillTot, BookNo, CustTel, IsDiscount, RmsStatus
        FROM RmInfo 
        WHERE RmNo = @RoomFromNo AND ShopId = @ShopId;
        
        -- 获取转入房间的信息
        DECLARE @ToRoomInfo TABLE (
            RtUp NVARCHAR(50),
            RoomToIkey NVARCHAR(50),
            BillTot INT,
            BookNo NVARCHAR(50),
            CustTel NVARCHAR(50),
            IsDiscount BIT
        );
        
        INSERT INTO @ToRoomInfo
        SELECT RtUp, RoomToIkey, BillTot, BookNo, CustTel, IsDiscount
        FROM RmInfo 
        WHERE RmNo = @RoomToNo AND ShopId = @ShopId;
        
        -- 更新转入房间信息（使用转出房间的客户信息）
        UPDATE RmInfo 
        SET RtUp = f.RtUp,
            RoomToIkey = f.RoomToIkey,
            BillTot = f.BillTot,
            BookNo = f.BookNo,
            CustTel = f.CustTel,
            IsDiscount = f.IsDiscount,
            RmsStatus = f.RmsStatus
        FROM @FromRoomInfo f
        WHERE RmInfo.RmNo = @RoomToNo AND RmInfo.ShopId = @ShopId;
        
        -- 清空转出房间信息
        UPDATE RmInfo 
        SET RtUp = t.RtUp,
            RoomToIkey = t.RoomToIkey,
            BillTot = t.BillTot,
            BookNo = t.BookNo,
            CustTel = t.CustTel,
            IsDiscount = t.IsDiscount,
            RmsStatus = 'E'  -- 设置为空房状态
        FROM @ToRoomInfo t
        WHERE RmInfo.RmNo = @RoomFromNo AND RmInfo.ShopId = @ShopId;
        
        -- 关键修改：只更新 OpenCacheInfo 中的物理房号，不更新计费房型
        -- 这样可以保持原始的计费标准（RtNo, RtName 保持不变）
        UPDATE OpenCacheInfo 
        SET FromRmNo = @RoomFromNo,
            RmNo = @RoomToNo
        WHERE Ikey = (SELECT RoomToIkey FROM @FromRoomInfo);
        
        -- 记录转房日志
        INSERT INTO RmExchange (ExchangeDate, FromRmNo, ToRmNo, UserName, ShopId)
        VALUES (GETDATE(), @RoomFromNo, @RoomToNo, @UserName, @ShopId);
        
        COMMIT TRANSACTION;
        
        SELECT 1 AS Result; -- 返回成功标识
        
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        
        -- 记录错误信息
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        DECLARE @ErrorSeverity INT = ERROR_SEVERITY();
        DECLARE @ErrorState INT = ERROR_STATE();
        
        RAISERROR(@ErrorMessage, @ErrorSeverity, @ErrorState);
        
        SELECT 0 AS Result; -- 返回失败标识
        
    END CATCH
END

-- 使用说明：
-- 1. 此存储过程专门用于"免费升级"场景的转房操作
-- 2. 与原有的 GetMRmInfo_Udp 存储过程的主要区别是：
--    - 原存储过程：更新 OpenCacheInfo 中的 RtNo, RtName（计费房型会改变）
--    - 新存储过程：只更新 OpenCacheInfo 中的 RmNo, FromRmNo（计费房型保持不变）
-- 3. 这样确保了客户在转房后仍按原始约定的房型标准计费
