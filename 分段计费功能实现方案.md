# 灵活转房计费功能实现方案

## 1. 核心问题与最终需求

经过反复讨论与澄清，我们确定“当房功能不智能”的核心问题在于：

**系统在处理转房时，缺乏灵活性，无法让前台操作员根据实际业务场景（如免费补偿或付费升级）来决定并修改顾客的最终计费方式。**

最终需求是赋予前台在转房操作时一个明确的**选择权**，而不是一个僵化的、自动化的计费系统。

## 2. 业务场景分析

前台在转房时需要处理两种核心业务场景：

1.  **场景A：免费升级 (按原房型计费)**
    - **情况**: 原房间设施有问题（如空调损坏），酒店为安抚顾客，免费将其转移到更好的房间。
    - **计费逻辑**: 顾客虽然物理上换了房，但其“计费合约”（即“当房”）应保持不变，后续所有计费**始终按照原房间的标准**进行。

2.  **场景B：付费升级 (按新房型计费)**
    - **情况**: 顾客主动要求升级到更好的房间，并同意支付更高的费用。
    - **计费逻辑**: 顾客的“计费合约”需要被更新。系统需要将**整个入住时段**（包括转房前的时间）**全部统一按新房间的更高标准**来重新计费。

## 3. 解决方案：赋予前台选择权

该方案的核心是改造**转房界面和逻辑**，将决策权交还给操作员。此方案无需修改复杂的结账逻辑。

### 阶段一：UI界面改造

- **目标文件**: `RMS2018\Controls\RoomChange.xaml`
- **修改内容**: 在转房窗口的“确认”按钮附近，添加一组单选按钮，让操作员明确选择计费模式。

  ```xml
  <!-- 示例XAML代码 -->
  <StackPanel Margin="0,10,0,0">
      <TextBlock Text="请选择转房后的计费方式:" FontWeight="Bold"/>
      <RadioButton x:Name="rbKeepRate" Content="按原房型计费 (免费升级)" IsChecked="False"/>
      <RadioButton x:Name="rbUpdateRate" Content="按新房型计费 (付费升级)" IsChecked="True"/>
  </StackPanel>
  ```

### 阶段二：后端逻辑改造

- **目标文件**: `RMS2018\Controls\RoomChange.xaml.cs`
- **修改内容**: 在转房按钮的点击事件处理程序中，增加一个判断逻辑，根据前台的选择，决定调用哪个数据库存储过程。

- **数据库改造**: 
  - **保留现有存储过程**: `GetMRmInfo_Udp` 的功能（更新房号、房型、价格）完全符合“付费升级”场景，无需改动。
  - **创建新的存储过程**: 复制 `GetMRmInfo_Udp` 并创建一个新版本，命名为 `GetMRmInfo_Udp_KeepRate`。这个新版本将只执行最核心的房间交换，**不修改** `OpenCacheInfo` 表中的计费信息（`RtNo`, `RtName`）。

    ```sql
    -- GetMRmInfo_Udp_KeepRate 存储过程中的核心更新语句
    -- 只更新物理房号和转房来源，不触碰计费房型
    UPDATE OpenCacheInfo SET FromRmNo=@RoomFromNo, RmNo=@RoomToNo WHERE Ikey=@RoomIkey
    ```

### 阶段三：C#代码实现

在 `RoomChange.xaml.cs` 的转房方法中，实现以下逻辑：

```csharp
// 伪代码示例
private void btnConfirmChange_Click(object sender, RoutedEventArgs e)
{
    // ... 省略获取房间信息等代码 ...

    try
    {
        if (rbUpdateRate.IsChecked == true) // 如果选择“付费升级”
        {
            // 调用老的、全功能更新的存储过程
            MRmInfoBll.GetMRmInfo_Up(shopid, EW.ToRmNo, EW.FromRmNo, EW.UserName);
        }
        else // 如果选择“免费升级”
        {
            // 调用新的、只换房号不改价格的存储过程
            MRmInfoBll.GetMRmInfo_Up_KeepRate(shopid, EW.ToRmNo, EW.FromRmNo, EW.UserName);
        }
        
        // 转房成功后的其他UI逻辑...
    }
    catch (Exception ex)
    {
        // 异常处理
    }
}
```

## 4. 总结

此方案通过对UI和数据库进行最小化、最精准的修改，完美实现了业务需求。它将复杂的业务决策权交还给最了解情况的前台员工，使系统变得既灵活又智能，同时避免了对核心结账逻辑进行颠覆性改造的风险。
