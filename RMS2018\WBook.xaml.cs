﻿using RMS2018.Controls;
using RMS2018.lib.Factory;
using RMS2018.PreservedWine;
using RMS2018.UChart;
using RMS2018.Utils;
using RMSBLL.MIMS;
using RMSBLL.RMS;
using RMSModel;
using RMSModel.API;
using RMSModel.DbFood;
using RMSModel.ExtensionRMS;
using RMSModel.MIMS;
using RMSModel.RMS;
using RMSUtils.Common;
using RMSUtils.RMS;
using RMSUtils.WebApi;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using Visifire.Charts;

namespace RMS2018
{
    /// <summary>
    /// WBook.xaml 的交互逻辑tbWeChatSearch_KeyDown
    /// </summary>
    public partial class WBook : Window
    {
        SingleRun single;
        EventHandling eh = new EventHandling();
        /// <summary>
        /// 顾客特殊需求
        /// </summary>
        List<CustDemandTitle> DemandList;
        /// <summary>
        /// 会员信息
        /// </summary>
        // MemberInfo memberinfo;
        private bool isBookStatusChecked = false; // 默认未勾选
        public WBook()
        {
            InitializeComponent();


        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {


            single = SingleRun.GetSingle();
            single.wbook = this;
            this.DataContext = single;


            single.vmpc.timelist = RMSTime.GetTime(VMTimeDate.GetWordDate(), single.vmpc.wechatuser.SetCheckIsMinisterUp(single.vmpc.vmuserinfo.muserinfo.UserId) ? 30 : 14);

            try
            {
                ptimeDate.SetData(single.vmpc.timelist, ptimeDate.scroll.ActualWidth / 7);//初始化日期信息
                ptimeDate.eSelectChang += SetSelectChangTimeAndShop;

                // 安全初始化门店信息
                if (VMShopInfo.shopList != null && VMShopInfo.shopList.Count > 0)
                {
                    pshop.SetData(VMShopInfo.shopList);//初始化门店信息
                }
                else
                {
                    // 如果shopList为空，创建默认数据
                    var defaultShopList = new List<MShopInfo>
                    {
                        new MShopInfo { ShopId = 0, ShopName = "预约中心" },
                        new MShopInfo { ShopId = 1, ShopName = "测试门店1" }
                    };
                    pshop.SetData(defaultShopList);
                }
                pshop.eSelectChang += SetSelectChangTimeAndShop;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"初始化门店信息失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                // 使用最基本的默认数据
                var fallbackShopList = new List<MShopInfo>
                {
                    new MShopInfo { ShopId = 1, ShopName = "默认门店" }
                };
                pshop.SetData(fallbackShopList);
                pshop.eSelectChang += SetSelectChangTimeAndShop;
            }
            SetShopIco();//根据登录门店隐藏门店列表

            pMenuType.eSelectChang += delegate ()
            {
                if (pMenuType.SelectData is MConTypeInfo)
                {
                    contypeinfo = pMenuType.SelectData as MConTypeInfo;
                    if (contypeinfo.CtModel == 2)
                    {
                        img_More.Visibility = Visibility.Collapsed;
                        pRoomType.Visibility = Visibility.Visible;
                        if (useRtShop != null)
                        {
                            tbUseRtInfo.Text = useRtShop.RtName;
                        }

                    }
                    else
                    {
                        img_More.Visibility = Visibility.Visible;
                        pRoomType.Visibility = Visibility.Collapsed;
                        bookNumber_TextChanged(null, null);//刷新推荐房型
                    }
                }
                //single.vmpc.vmrtprice.GetModel1(shoptimeSelect, shoptimeAddSelect, GetUserRtInfo());//重新获取价格体系

            };



            ptime.eSelectChang += delegate ()
            {
                SelectChang();

            };

            ptime.PreviewMouseLeftButtonDown += Item_MouseLeftButtonDown;

            pRoomType.eSelectChang += delegate ()
            {
                useRtShop = pRoomType.GetSelectVal<MRt_MShop>();//获取使用房型
                if (contypeinfo != null)
                {
                    if (pRoomType.Visibility == Visibility.Visible)
                    {
                        tbUseRtInfo.Text = useRtShop.RtName;
                    }
                    if (shoptimeSelect != null && useRtShop != null)
                    {
                        CustBehaviorManage.YearNightCou(custinfoInput, shoptimeSelect.TimeNo, useRtShop.RtName);
                    }
                    //if (pRoomType.Visibility == Visibility.Visible || contypeinfo.CtModel == 2)
                    //{
                    //    recommendRtShop = useRtShop;
                    //    tbUseRtInfo.Text = recommendRtShop.RtName;
                    //}
                }
                // single.vmpc.vmrtprice.GetModel1(shoptimeSelect, shoptimeAddSelect, GetUserRtInfo());//重新获取价格体系
            };

            pAddtime.eSelectChang += delegate ()
            {
                if (pAddtime.Visibility == Visibility.Visible)
                {
                    shoptimeAddSelect = pAddtime.GetSelectVal<MShopTimeInfoJoin>();//获取加钟时段
                    single.vmpc.vmrtprice.GetModel1(shoptimeSelect, shoptimeAddSelect, GetUserRtInfo());//重新获取价格体系
                    SetInitpMenuType(shopinfoSelect.ShopId, shoptimeSelect, shoptimeAddSelect);

                }
            };



            try
            {
                var phone = Phone_usb.GetClass();
                phone.callphone += delegate (string phones)
                {
                    this.Dispatcher.BeginInvoke(new Action(() =>
                    {
                        tbPhone.Text = phones;
                    }));
                };
                phone.bind();
            }
            catch (Exception ex)
            {

                MessageBox.Show("phoneusb erro:" + ex.Message);
            }


        }
        /// <summary>
        /// 当前选择门店
        /// </summary>
        MShopInfo shopinfoSelect;
        /// <summary>
        /// 当前选择预约时间
        /// </summary>
        RMSTime rmstimeSelect;
        /// <summary>
        /// 当前选择门店时段
        /// </summary>
        MShopTimeInfoJoin shoptimeSelect;
        /// <summary>
        /// 当前选择门店时段-加钟
        /// </summary>
        MShopTimeInfoJoin shoptimeAddSelect;
        /// <summary>
        /// 当前使用房型
        /// </summary>
        MRt_MShop useRtShop { get; set; }
        /// <summary>
        /// 推荐房型
        /// </summary>
        MRt_MShop recommendRtShop { get; set; }
        /// <summary>
        /// 顾客信息-填写的
        /// </summary>
        MCustInfo custinfoInput;
        /// <summary>
        /// 消费类型
        /// </summary>
        MConTypeInfo contypeinfo;
        /// <summary>
        /// 修改的预订记录信息
        /// </summary>
        bookcacheinfo_preorder bookUpdateInfo;

        /// <summary>
        /// 预订统计数
        /// </summary>
        List<MBookNumber> bookNumberList;
        /// <summary>
        /// 设置预约记录
        /// </summary>
        public void SetGridInfo()
        {
            // List<MBookCacheInfo> datalist = MBookCacheInfoBll.GetBook(shopinfoSelect.ShopId, rmstimeSelect.ComnDate, shoptimeSelect.TimeNo);
            List<bookcacheinfo_preorder> datalist = MBookCacheInfoBll.GetBook1(shopinfoSelect.ShopId, rmstimeSelect.ComnDate, shoptimeSelect.TimeNo);

            List<MShopInfo> shopList = VMShopInfo.shopList;
            foreach (var item in datalist)
            {
                item.BookShopName = VMShopInfo.GetShopInfo(item.BookShopId).ShopName;
            }
            dgBook.ItemsSource = datalist.OrderBy(i => i.IsDelete).ThenByDescending(i => i.BookDateTime).ToList();
            var datalistNotDel = datalist.FindAll(i => i.IsDelete == false);
            eh.CreateChart("房型", GridP0, RenderAs.Pie, datalistNotDel.Where(i => i.CtNo != 2).GroupBy(i => i.RtName).Select(g => (new UChartConent { name = g.Key, value = g.Count() })).ToList());
            eh.CreateChart("消费类型", GridP1, RenderAs.Pie, datalistNotDel.GroupBy(i => i.CtName).Select(g => (new UChartConent { name = g.Key, value = g.Count() })).ToList());
            eh.CreateChart("到达时间", GridP2, RenderAs.Pie, datalistNotDel.GroupBy(i => i.ComeTime).Select(g => (new UChartConent { name = g.Key, value = g.Count() })).ToList());
            eh.CreateChart("人数", GridP3, RenderAs.Pie, SetBookNumberSum(datalistNotDel));
            eh.CreateChart("时段", GridP4, RenderAs.Pie, SetBookTimeSum());
        }
        /// <summary>
        /// 时段停订检测
        /// </summary>
        public void SetCheckStop()
        {
            /// single.vmpc.vmuserinfo.ShopId != 0   预约中心不受限
            if (single.vmpc.vmuserinfo.ShopId != 0 && shopinfoSelect.ShopId != single.vmpc.vmuserinfo.ShopId && shoptimeSelect.IsEnabled == false)
            {//如果登录跟预约信息不同，并超订时则关闭预约按钮
                btnBook.Content = "本时段已停订!";
                btnBook.IsEnabled = false;
            }
            else
            {
                btnBook.Content = "预约";
                btnBook.IsEnabled = true;
            }
        }
        /// <summary>
        /// 选段选择变更，并绑定dataGrid
        /// </summary>
        private void SelectChang()
        {
            shoptimeSelect = ptime.GetSelectVal<MShopTimeInfoJoin>();//时段信息
            if (shopinfoSelect != null && rmstimeSelect != null && shoptimeSelect != null)
            {

                SetCheckStop();
                SetInitpMenuType(shopinfoSelect.ShopId, shoptimeSelect, shoptimeAddSelect);
                SetInitRtInfo();
                //-------
                SetGridInfo();
                dtpicker.SetTimeDate(shoptimeSelect.BegTime);
                single.vmpc.vmRtInfo.SetRtBookNumber(shoptimeSelect.TimeNo, bookNumberList);//刷新房型数量
                bookNumber_TextChanged(null, null);//刷新推荐房型
                single.vmpc.vmrtprice.GetModel1(shoptimeSelect, shoptimeAddSelect, GetUserRtInfo());//重新获取价格体系
                if (pAddtime.Visibility == Visibility.Visible)
                {
                    SetAddTimeInfo();//刷新直落时间
                }



            }
        }
        /// <summary>
        /// 日期选择-门店选择
        /// </summary>
        public void SetSelectChangTimeAndShop()
        {
            if (ptimeDate.SelectData != null && pshop.SelectData != null)
            {
                rmstimeSelect = ptimeDate.GetSelectVal<RMSTime>();
                shopinfoSelect = pshop.GetSelectVal<MShopInfo>();
                SetBookNoInfo();
                single.vmpc.vmuserinfo.vmbookNo.GetLocalBookNo(rmstimeSelect.ComnDate, shopinfoSelect.ShopId);//获取预约号
                single.vmpc.vmShopInfo.SetShopCheck(rmstimeSelect.chineseCalendarBase.Date);//门店停订检测
                SetInitShopTime(shopinfoSelect.ShopId, rmstimeSelect.chineseCalendarBase.Date);//初始化时段信息
                bookNumberList = single.vmpc.GetBookNumber(shopinfoSelect.ShopId, rmstimeSelect.ComnDate, single.vmpc.vmShopTimeInfo.timeList);//重新获取现有的房间占用情况
            }
        }


        /// <summary>
        /// 初始化时段信息
        /// </summary>
        public void SetInitShopTime(int shopid, DateTime date)
        {
            ICollection controlList = single.vmpc.vmShopTimeInfo.GetNewTime(shopid, date, single.vmpc.vmuserinfo.ShopId.ToString());
            if (controlList != null)
                ptime.SetData(controlList, 110);
        }
        /// <summary>
        /// 换档检测
        /// </summary>
        public void SetChangeTime()
        {
            if (rmstimeSelect != null)
            {
                DateTime ntime = VMTimeDate.GetWordDate();
                if (rmstimeSelect.chineseCalendarBase.Date.Day == ntime.Day)
                {
                    List<MShopTimeInfoJoin> timelist = single.vmpc.vmShopTimeInfo.timeList;
                    if (timelist != null)
                    {
                        foreach (var item in timelist)
                        {
                            int timenumber = int.Parse(DateTime.Now.ToString("HHmm"));
                            if (item.BegTime > 900 && item.EndTime > 900 && timenumber > item.EndTime)
                            {
                                this.Dispatcher.Invoke(new Action(() =>
                                {
                                    SetSelectChangTimeAndShop();
                                }));
                                break;
                            }

                        }
                    }
                }
            }
        }
        /// <summary>
        /// 初始化消费类型
        /// </summary>
        public void SetInitpMenuType()
        {
            ICollection controlList = single.vmpc.vmConTypeInfo.listConTypeall;
            if (controlList != null)
                pMenuType.SetData(controlList, 200);
        }
        /// <summary>
        /// 初始化消费类型
        /// </summary>
        public void SetInitpMenuType(int shopid, MShopTimeInfoJoin timeinfo, MShopTimeInfoJoin shoptimeAddSelect)
        {
            ICollection controlList = single.vmpc.vmConTypeInfo.GetConType(shopid, timeinfo, shoptimeAddSelect);
            if (controlList != null)
                pMenuType.SetData(controlList, 200);
        }
        /// <summary>
        /// 初始化房型类型
        /// </summary>
        public void SetInitRtInfo()
        {
            pRoomType.SetData(single.vmpc.vmRtInfo.GetRtInfo(shopinfoSelect.ShopId));
        }


        /// <summary>
        /// 直落操作
        /// </summary>
        public void SetAddTime()
        {
            ///判断是否加钟状态
            if (pAddtime.Visibility == Visibility.Visible)
            {
                pAddtime.Visibility = Visibility.Collapsed;
                btnAddTime.Content = "直落";
                shoptimeAddSelect = null;//清空直落选择
                single.vmpc.vmrtprice.GetModel1(shoptimeSelect, shoptimeAddSelect, GetUserRtInfo());//重新获取价格体系
            }
            else
            {
                SetAddTimeInfo();

            }


        }
        /// <summary>
        /// 显示直落信息
        /// </summary>
        public void SetAddTimeInfo(string SelelctNo = null)
        {
            List<MShopTimeInfoJoin> list = single.vmpc.vmShopTimeInfo.GetAddNewTime(shoptimeSelect);
            if (string.IsNullOrEmpty(SelelctNo) == false)
            {
                var timeinfo = list.Find(i => i.TimeNo == SelelctNo);
                if (timeinfo != null)
                    timeinfo.IsChecked = true;
            }
            if (list == null || list.Count == 0)
            {//如果没有直落数据则重刷页面
                shoptimeAddSelect = null;
                single.vmpc.vmrtprice.GetModel1(shoptimeSelect, shoptimeAddSelect, GetUserRtInfo());//重新获取价格体系

            }
            pAddtime.SetData(list, 150);
            pAddtime.Visibility = Visibility.Visible;
            btnAddTime.Content = "取消直落";

        }







        /// <summary>
        /// 点击时段时触发时间
        ///     当加钟模块显示时则点击一下可触发加钟刷新，否则双击两下才触发
        ///     
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Item_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 2)
            {
                SetAddTime();
            }
        }

        /// <summary>
        /// 绑定顾客信息
        /// </summary>
        public void SetCustInfo()
        {
            if (custinfoInput != null)
            {
                tbName.Text = custinfoInput.CustName;
                tbPhone.Text = custinfoInput.CustTel;
                rbSex1.IsChecked = (custinfoInput.CustSex == "先生");
                rbSex2.IsChecked = !rbSex1.IsChecked;
            }
        }

        /// <summary>
        /// 清空填写数据
        /// </summary>
        public void SetClearInput()
        {
            tbName.Text = string.Empty;
            tbPhone.Text = string.Empty;
            bookNumber.Text = "2";
            tbBookMemory.Text = "";
            cbDiscount.IsChecked = false;
        }
        /// <summary>
        /// 获取顾客填写信息
        /// </summary>
        public MCustInfo GetCustInfo()
        {
            if (custinfoInput == null)
            {
                custinfoInput = new MCustInfo() { Custcompany = string.Empty, OpenId = string.Empty };
            }
            custinfoInput.CustName = tbName.Text;
            custinfoInput.CustTel = tbPhone.Text;
            custinfoInput.CustSex = rbSex1.IsChecked == true ? rbSex1.Content.ToString() : rbSex2.Content.ToString();
            return custinfoInput;
        }
        /// <summary>
        /// 预约/修改预约  //lsf-20250703
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnBook_Click(object sender, RoutedEventArgs e)
        {

            btnBook.IsEnabled = false;
            Thread thread = new Thread(delegate ()
            {

                MCustInfo custinfo = null;
                MBookCacheInfo book = null;
                try
                {
                    // 第一次Dispatcher.Invoke块
                    this.Dispatcher.Invoke(new Action(() =>
                    {
                        MShopTimeInfoJoin shoptimeadd = pAddtime.GetSelectVal<MShopTimeInfoJoin>();
                        MConTypeInfo ctinfo = pMenuType.GetSelectVal<MConTypeInfo>();
                        custinfo = GetCustInfo();

                        if (tbPhone.Islegitimate == false)
                        {
                            MessageBox.Show("请完善顾客信息!");
                            btnBook.IsEnabled = true; // 恢复按钮状态
                            return;
                        }

                        #region 获取房型信息
                        MRt_MShop rtinfo = GetUserRtInfo();
                        if (rtinfo == null)
                        {
                            MessageBox.Show("相应房间已订满!");
                            btnBook.IsEnabled = true; // 恢复按钮状态
                            return;
                        }
                        else
                        {
                            if (SingleRun.GetSingle().vmpc.vmuserinfo.ShopId != shopinfoSelect.ShopId)
                            {
                                if (rtinfo.mBookNumber != null && rtinfo.mBookNumber.BookSurplus - 1 < 0)
                                {
                                    MessageBox.Show(rtinfo.RtName + "已满!");
                                    btnBook.IsEnabled = true; // 恢复按钮状态
                                    return;
                                }
                            }
                        }
                        #endregion

                        #region 直落时段获取
                        MShopTimeInfoJoin shoptimeAdd = null;
                        if (shoptimeAddSelect != null)
                        {
                            shoptimeAdd = shoptimeAddSelect;
                        }
                        else
                        {
                            shoptimeAdd = shoptimeSelect;
                        }
                        #endregion

                        book = new MBookCacheInfo();
                        book.ShopId = shopinfoSelect.ShopId;
                        book.ComeDate = rmstimeSelect.ComnDate;
                        book.ComeTime = dtpicker.TimeData;
                        book.Beg_Key = shoptimeSelect.TimeNo;
                        book.Beg_Name = shoptimeSelect.TimeName;
                        book.End_Key = shoptimeAdd.TimeNo;
                        book.End_Name = shoptimeAdd.TimeName;
                        book.Numbers = bookNumber.Number;
                        book.RtNo = rtinfo.RtNo;
                        book.RtName = rtinfo.RtName;
                        book.CtNo = ctinfo.CtNo;
                        book.CtName = ctinfo.CtName;
                        book.PtNo = 1;
                        book.PtName = "PT类型";
                        book.BookMemory = tbBookMemory.Text;
                        book.Val1 = cbDiscount.IsChecked == true ? 1 : 0;
                        book.CallTel = VMPC.CallTel;
                        WeChatUser weChatUser = uwechatSearch.GetSelectUser();
                        book.OrderUserID = weChatUser.UserId;
                        book.OrderUserName = weChatUser.name;
                        book.DemandNumber = bookDemand.GetInsertNumber();
                        /*  如果 年卡消费 被勾选，则设置 AnnualCard =0*/

                        if (isBookStatusChecked)
                        {
                            book.AnnualCard = "0";
                            book.DemandNumber = -1;
                            /* Application.Current.Properties["_annualCard"] = false;*/
                        }
                        if (BookInfo != null)
                        {
                            book.BingingIkey = BookInfo.iKey.ToString();
                            SetBook_old(null);
                        }
                    }));

                    // 非UI线程操作
                    if (bookUpdateInfo == null)
                    {
                        if (book != null)
                        {
                            this.Dispatcher.Invoke(new Action(() =>
                            {
                                single.vmpc.SetCustBook(custinfo, book);
                                ucustinfo.umemberinfo.SetInsertDate(book.Ikey);


                                if (book.DemandNumber > 0)
                                {
                                    bookDemand.SetInsertInfo(book.Ikey);
                                }
                            }));
                        }
                    }
                    else
                    {
                        if (custinfo != null && book != null)
                        {
                            if (custinfo.CustKey.ToLower() != bookUpdateInfo.CustKey.ToLower())
                            {
                                this.Dispatcher.Invoke(new Action(() =>
                                {
                                    MessageBox.Show("修改失败：暂不支持电话号码变更操作!");
                                    btnBook.IsEnabled = true;
                                }));
                                return;
                            }
                        }
                        this.Dispatcher.Invoke(new Action(() =>
                        {
                            single.vmpc.SetCustBookUpdate(custinfo, book, bookUpdateInfo);
                            if (book.DemandNumber > 0)//测试
                            {
                                bookDemand.SetUpdateInfo(bookUpdateInfo.Ikey);
                            }
                        }));
                    }

                    // 第二次Dispatcher.Invoke块
                    this.Dispatcher.Invoke(new Action(() =>
                    {
                        if (book != null)
                        {
                            List<bookcacheinfo_preorder> booklist = dgBook.ItemsSource as List<bookcacheinfo_preorder>;
                            if (booklist != null)
                            {
                                foreach (var item in booklist)
                                {
                                    if (item.Ikey == book.Ikey)
                                    {
                                        dgBook.SelectedItem = item;
                                        dgBook.ScrollIntoView(item);
                                        break;
                                    }
                                }
                            }
                        }
                        SetClearInput();
                        SelectChang();
                        bookUpdateInfo = null;
                        if (pAddtime.Visibility == Visibility.Visible)
                            SetAddTime();
                        SetShopIco();
                    }));

                    uwinsock.CleanData();
                }
                catch (Exception ex)
                {
                    this.Dispatcher.Invoke(new Action(() =>
                    {
                        MessageBox.Show("预约过程发生异常：" + ex.Message);
                        btnBook.IsEnabled = true;
                    }));
                }
                finally
                {
                    this.Dispatcher.Invoke(new Action(() =>
                    {
                        btnBook.IsEnabled = true;
                        btnBookUpdateNo_Click(null, null);
                    }));
                }
            });

            thread.IsBackground = true; // 设置为后台线程
            thread.Start();

        }

        /// <summary>
        /// 获取正在使用的房间类型
        /// </summary>
        /// <returns></returns>
        public MRt_MShop GetUserRtInfo()
        {
            MRt_MShop rtinfo = null;
            //if (contypeinfo != null)
            //{
            rtinfo = recommendRtShop;
            if (pRoomType.Visibility != Visibility.Visible)
            {//使用推荐房型
                rtinfo = recommendRtShop;
            }
            else
            {//使用选择房型
                rtinfo = useRtShop;
            }
            // }


            return rtinfo;
        }
        /// <summary>
        /// 根据电话获取预约记录并选中
        /// </summary>
        /// <param name="tel"></param>
        public void GetBookInfo_select(string tel)
        {
            bookcacheinfo_preorder selectbook = dgBook.SelectedItem as bookcacheinfo_preorder;
            bool isExists = false;
            if (selectbook != null && selectbook.CustTel == tel)
            {//如果当前已经选中此行则退出
                isExists = true;
            }
            tbUbnumber.Visibility = Visibility.Collapsed;
            List<bookcacheinfo_preorder> booklist = dgBook.ItemsSource as List<bookcacheinfo_preorder>;
            if (string.IsNullOrEmpty(tel) == false && booklist != null)
            {
                int count = 0;
                int delcount = 0;
                foreach (var item in booklist)
                {

                    if (item.CustTel == tel)
                    {
                        //2021-01-14 jjy 修改预约查询结果
                        if (uBookSearch.IsBookNoSearch)//预约号查询
                        {
                            if (isExists == false && item.BookNo == uBookSearch.bookno)
                            {
                                dgBook.SelectedItem = item;
                                dgBook.ScrollIntoView(item);
                                uBookSearch.IsBookNoSearch = false;
                                uBookSearch.bookno = "";
                            }
                        }
                        else if (isExists == false && count == 0)//非预约号查询
                        {
                            dgBook.SelectedItem = item;
                            dgBook.ScrollIntoView(item);
                        }
                        //if (isExists == false && count == 0)
                        //{
                        //    dgBook.SelectedItem = item;
                        //    dgBook.ScrollIntoView(item);
                        //}
                        if (item.IsDelete == true)
                        {
                            delcount++;
                        }
                        count++;
                    }
                }
                if (count > 0)
                {
                    tbUbnumber.Text = string.Format("电话:{0},找到‘{1}’条预约记录{2}", tel, count, delcount > 0 ? "，其中‘" + delcount + "’条删除记录" : "");

                    tbUbnumber.Visibility = Visibility.Visible;
                }

                try
                {
                    int nowBookCount = MBookCacheInfoBll.GetNowDayBookCount(rmstimeSelect.ComnDate, tel);
                    if (nowBookCount > 0)
                    {
                        tbUbnumber.Text += string.Format("，当天普通预约有{0}条记录!", nowBookCount);
                        if (tbUbnumber.Visibility != System.Windows.Visibility.Visible)
                        {
                            tbUbnumber.Visibility = Visibility.Visible;

                        }
                    }
                }
                catch (Exception)
                {

                }

                //try
                //{
                //    //20240918增加顾客行为读取
                //    var CustBehavior = MCustInfoBll.GetCustBehavior(tel);
                //    if (CustBehavior != null && string.IsNullOrEmpty(CustBehavior.BehaviorMsg) == false)
                //    {
                //        MessageBox.Show(CustBehavior.BehaviorMsg, "提示", MessageBoxButton.OK, MessageBoxImage.Information);
                //    }
                //}
                //catch { }
            }

        }
        /// <summary>
        /// 电话号码检查，当号码等于11未时检测是否存在顾客信息
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tbPhone_TextChanged(object sender, TextChangedEventArgs e)
        {
            //string phone = tbPhone.Text;
            //if (phone.Length == 11)
            //{
            //    tbPhone.IsEnabled = false;
            //    Thread thread = new Thread(delegate ()
            //    {
            //        try
            //        {
            //            custinfoInput = single.vmpc.vmCustInfo.GetCustPhoneInfo(phone);
            //            this.Dispatcher.Invoke(new Action(() =>
            //            {
            //                if (custinfoInput != null && custinfoInput.BechaNumber > 0)
            //                {
            //                    btnCustBehaInfo_Click(null, null);//打开顾客行为
            //                }
            //                if (isMemberCheck.IsChecked == true)
            //                {
            //                    ucustinfo.umemberinfo.SetMemberInfoBingd(phone);//绑定会员信息
            //                }
            //                SetCustInfo();//绑定顾客信息
            //                ucustinfo.DataContext = custinfoInput;
            //                tbPhone.IsEnabled = true;
            //                tbPhone.Focus();
            //                tabCustInfo.SelectedIndex = 0;
            //            }));
            //        }
            //        catch (Exception ex)
            //        {
            //            this.Dispatcher.Invoke(new Action(() =>
            //            {
            //                tbPhone.IsEnabled = true;
            //            }));
            //            MessageBox.Show("异常：" + ex.Message, "提示", MessageBoxButton.OK, MessageBoxImage.Error);
            //        }
            //    });
            //    thread.Start();

            //}
            //else
            //{
            //    custinfoInput = null;
            //    // tbName.Text = string.Empty;
            //    ucustinfo.umemberinfo.SetMemberInfoClear();
            //    ucustinfo.DataContext = custinfoInput;
            //}

        }
        /// <summary>
        /// 加钟按钮点击
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>

        private void btnAddTime_Click(object sender, RoutedEventArgs e)
        {
            SetAddTime();

        }

        /// <summary>
        /// 人数变更检测-自动匹配人数相关房型
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void bookNumber_TextChanged(object sender, TextChangedEventArgs e)
        {
            recommendRtShop = single.vmpc.vmRtInfo.GetBookNumberRt(bookNumber.Number);
            if (pRoomType.Visibility != Visibility.Visible)
            {
                if (recommendRtShop != null)
                {
                    tbUseRtInfo.Text = recommendRtShop.RtName;
                }
                else
                {
                    tbUseRtInfo.Text = "没有合适的房型";
                }
            }

        }

        /// <summary>
        /// 删除预约记录
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void MDel_Click(object sender, RoutedEventArgs e)
        {
            bookcacheinfo_preorder bookInfo = dgBook.SelectedItem as bookcacheinfo_preorder;
            if (bookInfo != null)
            {
                if (bookInfo.IsDelete == true) { MessageBox.Show("该预约信息已删除，不可再次删除"); return; }
                if (bookInfo.BookUserName == "在线预订" && !string.IsNullOrEmpty(bookInfo.ActualPayCash)) { MessageBox.Show("在线预订记录不可删除!"); return; }
                Window_Pwd pwd = new Window_Pwd("删除确认密码：123", "123");
                if (pwd.Blag == true)
                {
                    bookInfo.IsDelete = true;
                    bookInfo.DelUserName = single.vmpc.vmuserinfo.muserinfo.UserName;
                    int result = MBookCacheInfoBll.SetDelUpdate(bookInfo);
                    if (result > 0)
                    {
                        List<bookcacheinfo_preorder> list = dgBook.ItemsSource as List<bookcacheinfo_preorder>;
                        dgBook.ItemsSource = null;
                        dgBook.ItemsSource = list;
                        //list.Remove(bookInfo);

                    }
                }
            }
        }
        /// <summary>
        /// 修改预约记录
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void MUpdate_Click(object sender, RoutedEventArgs e)
        {
            bookUpdateInfo = dgBook.SelectedItem as bookcacheinfo_preorder;
            if (bookUpdateInfo != null)
            {
                if (bookUpdateInfo.IsDelete == true) { MessageBox.Show("该预约信息已删除，不可修改"); return; }
            }

            SetUpdateBook(bookUpdateInfo);
        }
        /// <summary>
        /// 短信重发
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void MMsgSend_Click(object sender, RoutedEventArgs e)
        {
            MBookCacheInfo bookinfo = dgBook.SelectedItem as MBookCacheInfo;
            if (bookinfo != null)
            {
                if (bookinfo.IsDelete == true) { MessageBox.Show("该预约信息已删除，不可重发短信"); return; }
                Window_Pwd pwd = new Window_Pwd("短信重发确认密码：123", "123");
                if (pwd.Blag == true)
                {
                    SetUpdateBook(bookUpdateInfo);
                    MBookNumberCount mbookNumberCount = MBookCacheInfoBll.GetBookNumber(bookinfo.ComeDate, bookinfo.BookShopId);
                    RMSUtils.PhoneMsg.PhoneSMS.SetBookMsg(bookinfo, mbookNumberCount);//发送短信
                }
            }

        }
        /// <summary>
        /// 预约信息修改
        /// </summary>
        /// <param name="bookUpdateInfo">预约信息</param>
        public void SetUpdateBook(bookcacheinfo_preorder bookUpdateInfo)
        {
            if (bookUpdateInfo != null)
            {
                if (tbPhone.Text == bookUpdateInfo.CustTel)
                {
                    tbName.Text = bookUpdateInfo.CustName.Replace("先生", "").Replace("女士", "");

                }
                if (bookUpdateInfo.DemandNumber > 0)
                {//检测是否存在特殊要求
                    bookDemand.SetBingingInfo(bookUpdateInfo.Ikey);
                }
                uwechatSearch.SetClearInputConent();
                if (string.IsNullOrEmpty(bookUpdateInfo.OrderUserName) == false)
                {
                    uwechatSearch.SetConentBing_book(bookUpdateInfo.OrderUserID, bookUpdateInfo.OrderUserName);
                }
                if (bookUpdateInfo.Beg_Key != bookUpdateInfo.End_Key)
                {//顾客当时选择了直落
                    SetAddTimeInfo(bookUpdateInfo.End_Key);
                }
                else if (pAddtime.Visibility == Visibility.Visible)
                {
                    SetAddTime();
                }

                gridBookUpdate.Visibility = Visibility.Visible;
                btnBook.Visibility = Visibility.Collapsed;
                tbPhone.Text = bookUpdateInfo.CustTel;
                bookNumber.Text = bookUpdateInfo.Numbers.ToString();
                tbBookMemory.Text = bookUpdateInfo.BookMemory;
                cbDiscount.IsChecked = bookUpdateInfo.Val1 == 1;
                single.vmpc.vmConTypeInfo.listConTypeall.Find(i => i.CtNo == bookUpdateInfo.CtNo).IsChecked = true;
                single.vmpc.vmRtInfo.rtList.Find(i => i.RtNo == bookUpdateInfo.RtNo).IsChecked = true;

                try
                {
                    //   int ComnTime = int.Parse(bookUpdateInfo.ComeTime.Replace(":", ""));
                    dtpicker.SetTimeDate(bookUpdateInfo.ComeTime);
                }
                catch
                {
                }
                single.vmpc.vmuserinfo.vmbookNo.BookNoUpdate = bookUpdateInfo.BookNo;
                bookNoUpdate.Visibility = Visibility.Visible;
                SetBookNoInfo();
                if (bookUpdateInfo.ShopId != single.vmpc.vmuserinfo.ShopId)
                {
                    btnShopShrink_MouseLeftButtonUp(null, null);
                }

            }
            else
            {
                btnBookUpdateNo_Click(null, null);
            }
        }

        /// <summary>
        /// 确定修改预订信息
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>

        private void btnBookUpdateOk_Click(object sender, RoutedEventArgs e)
        {
            btnBook_Click(null, null);//触发预订事件

        }
        /// <summary>
        /// 取消修改预订信息
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>

        private void btnBookUpdateNo_Click(object sender, RoutedEventArgs e)
        {
            if (pAddtime.Visibility == Visibility.Visible)
            {//取消预约关闭直落按钮
                SetAddTime();
            }
            uwechatSearch.SetClearInputConent();
            gridBookUpdate.Visibility = Visibility.Collapsed;
            btnBook.Visibility = Visibility.Visible;
            bookUpdateInfo = null;//清空已选的修改记录
            SetClearInput();
            bookNoUpdate.Visibility = Visibility.Collapsed;
            SetBookNoInfo();
            bookDemand.SetClear();
            SetCheckStop();
        }

        //private void btnClose_Click(object sender, RoutedEventArgs e)
        //{
        //    bool result = Msg.showDialog("确定退出程序？");
        //    if (result == true)
        //    {
        //        uwinsock.UClose();
        //        single.vmpc.client.SetLoginInfo_Off();

        //        Environment.Exit(0);
        //    }
        //}

        private void btnOpen_Click(object sender, RoutedEventArgs e)
        {
            var result = WindowBuildFactory.CreateWindow(EWindowUi.开房);

            //bool result = single.SetWindowOpen(EWindowUi.开房);
            if (result != null)
                this.Hide();

        }


        /// <summary>
        /// 双击预订表格，进行修改
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void dgBook_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {

            if (e.ClickCount == 2)
            {
                MBookCacheInfo bookUpdateInfo = dgBook.SelectedItem as MBookCacheInfo;
                if (bookUpdateInfo != null && bookUpdateInfo.IsInvalid == false)
                {
                    MUpdate_Click(null, null);
                }
            }
        }

        /// <summary>
        /// 预约信息查询
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnBookSearch_Click(object sender, RoutedEventArgs e)
        {
            if (this.tbPhone.Text.Length == 11)
            {
                uBookSearch.tbPhone.Text = this.tbPhone.Text;
                uBookSearch.SetSearch();

            }
            uBookSearch.Visibility = Visibility.Visible;
        }

        private void uBookSearch_onGridClick(bookcacheinfo_preorder bookCacheInfo)
        {
            try
            {
                single.vmpc.timelist.Find(i => i.ComnDate == bookCacheInfo.ComeDate).IsChecked = true;
                VMShopInfo.shopList.Find(i => i.ShopId == bookCacheInfo.ShopId).IsChecked = true;
                bookUpdateInfo = bookCacheInfo;
                SetUpdateBook(bookCacheInfo);
            }
            catch
            {
            }
        }

        /// <summary>
        /// 顾客行为按钮点击
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnCustBehaInfo_Click(object sender, RoutedEventArgs e)
        {
            uCustBehaInfo.Visibility = Visibility.Visible;
            uCustBehaInfo.CustomInfo(custinfoInput);
        }

        /// <summary>
        /// 门店收缩
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnShopShrink_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            pshop.Visibility = Visibility.Visible;
            btnShopShrink.Visibility = Visibility.Collapsed;
            btnShopHidden.Visibility = Visibility.Visible;
        }

        /// <summary>
        /// 内部消息点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnMsg_Click(object sender, RoutedEventArgs e)
        {
            //usms.Visibility = Visibility.Visible;
            // single.SetWindowOpen(EWindowUi.信息);
            WindowBuildFactory.CreateWindow(EWindowUi.信息);

            btnMsgEllipse.Visibility = Visibility.Collapsed;

        }

        public void SetNewMsg(MSMS _sms)
        {
            usmsinfo.SetMsg(_sms);
            btnMsgEllipse.Visibility = Visibility.Visible;
        }

        private void dgBook_LoadingRow(object sender, DataGridRowEventArgs e)
        {
            //   MBookCacheInfo dataRow = e.Row.Item as MBookCacheInfo;
            //2020-12-18 jjy 添加已预下单背景色显示浅粉色
            bookcacheinfo_preorder dataRow = e.Row.Item as bookcacheinfo_preorder;
            dataRow.SetGridColor_New();
        }

        /// <summary>
        /// 设置预约号信息显示，如果当前是修改模式则显示两个预约号
        /// </summary>
        public void SetBookNoInfo()
        {
            if (bookUpdateInfo != null)
            {//修改预约信息模式下只要跟修改预约日期-门店不匹配则显示新预约号
                if (bookUpdateInfo.ShopId == shopinfoSelect.ShopId && bookUpdateInfo.ComeDate == rmstimeSelect.ComnDate)
                    Grid.SetColumn(bookNoUpdate, 0);
                else
                    Grid.SetColumn(bookNoUpdate, 1);
            }
            if (bookNoUpdate.Visibility == Visibility.Visible)
                Grid.SetColumnSpan(gridBook, 1);
            else
                Grid.SetColumnSpan(gridBook, 2);
        }

        /// <summary>
        /// 推荐房型变更事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tbUseRtInfo_TextChanged(object sender, TextChangedEventArgs e)
        {
            single.vmpc.vmrtprice.GetModel1(shoptimeSelect, shoptimeAddSelect, GetUserRtInfo());//重新获取价格体系
        }
        /// <summary>
        /// 门店图标设置
        /// </summary>

        public void SetShopIco()
        {
            if (single.vmpc.vmuserinfo.ShopId != 0)
            {//非预约中心
                VMShopInfo.shopList.Find(i => i.ShopId == single.vmpc.vmuserinfo.ShopId).IsChecked = true;
                pshop.Visibility = Visibility.Collapsed;
                btnShopShrink.Visibility = Visibility.Visible;
            }
        }


        private void gridCustBehaInfo_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            uCustBehaInfo.Visibility = Visibility.Collapsed;
        }

        private void tbPhone_TextChanged_1(MCustInfo custinfo)
        {


            if (custinfo != null)
            {
                try
                {
                    custinfoInput = custinfo;
                    //if (custinfoInput != null && custinfoInput.BechaNumber > 0)
                    //{
                    //    btnCustBehaInfo_Click(null, null);//打开顾客行为
                    //}
                    GetBookInfo_select(custinfo.CustTel);
                    if (WindowAbility.VipDistinguish == true)
                    {
                        ucustinfo.umemberinfo.SetMemberInfoBingd(custinfoInput.CustTel);//绑定会员信息
                    }
                    SetCustInfo();//绑定顾客信息
                    ucustinfo.DataContext = custinfoInput;
                    tabCustInfo.SelectedIndex = 0;
                    if (shoptimeSelect != null && useRtShop != null)///11111
                    {
                        CustBehaviorManage.YearNightCou(custinfoInput, shoptimeSelect.TimeNo, useRtShop.RtName);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show("异常：" + ex.Message, "提示", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            else
            {
                custinfoInput = null;
                GetBookInfo_select(null);
                // tbName.Text = string.Empty;
                ucustinfo.umemberinfo.SetMemberInfoClear();
                ucustinfo.DataContext = custinfoInput;
            }
        }
        /// <summary>
        /// 快捷用语点击事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>

        private void BtnShortcutMsg_Click(object sender, RoutedEventArgs e)
        {
            ubookmsg.SetOpenControl(tbBookMemory.Text);
        }
        /// <summary>
        /// 内容文本选择
        /// </summary>
        /// <param name="conent"></param>
        private void ubookmsg_SelectValue(string conent)
        {
            tbBookMemory.Text = conent;
        }

        /// <summary>
        /// 鼠标点击预订备注文本控件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void tbBookMemory_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            BtnShortcutMsg_Click(null, null);
        }
        /// <summary>
        /// 更多房型按钮点击
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void img_More_MouseRightButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (contypeinfo != null && contypeinfo.CtModel == 1)
            {
                if (pRoomType.Visibility != Visibility.Visible)
                {

                    pRoomType.Visibility = Visibility.Visible;
                }
                else
                {
                    pRoomType.Visibility = Visibility.Collapsed;
                }
                tbUseRtInfo.Text = GetUserRtInfo().RtName;

            }

        }

        private void BtnBookDemand_Click(object sender, RoutedEventArgs e)
        {
            bookDemand.Visibility = Visibility.Visible;
            bookDemand.SetBingingInfo(this.DemandList);

        }

        /// <summary>
        /// 特殊需求选择
        /// </summary>
        /// <param name="checkBoxList"></param>
        private void bookDemand_SelectValue(List<CustDemandTitle> DemandList)
        {

            if (DemandList != null && DemandList.Count > 0)
            {
                tbDemandNumber.Text = DemandList.Count.ToString();
                gridDemand.Visibility = Visibility.Visible;
            }
            else
            {
                gridDemand.Visibility = Visibility.Collapsed;
            }
            this.DemandList = DemandList;
        }

        /// <summary>
        /// 电话绑定
        /// </summary>
        /// <param name="tel"></param>
        private void uwinsock_BingingPhone(string tel)
        {
            tbPhone.Text = tel;
        }
        /// <summary>
        /// 顾客来电
        /// </summary>
        /// <param name="tel"></param>
        private void uwinsock_PhoneCall(string tel)
        {

        }
        WRms2009Book rms2009book;
        /// <summary>
        /// 旧数据处理
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnBook_Old_Click(object sender, RoutedEventArgs e)
        {

            if (rms2009book == null)
            {

                //  rms2009book = new WRms2009Book(rmstimeSelect.ComnDate);
                rms2009book = new WRms2009Book(VMTimeDate.GetWordDate().ToString("yyyyMMdd"));
                rms2009book.SelectValue += SetBook_old;
            }
            //rms2009book.SerchBydateAndCustTel(rmstimeSelect.ComnDate, tbPhone.Text);
            rms2009book.BtnNowInfo_Copy_Click(null, null);
            rms2009book.Show();


        }
        BookCacheInfo BookInfo = null;
        /// <summary>
        /// 设置旧数据
        /// </summary>
        public void SetBook_old(BookCacheInfo BookInfo)
        {
            //if (BookInfo == null)
            //{
            //    btnBook_Old.Background = Brushes.White;
            //}
            //else
            //{
            //    tbBookMemory.Text = BookInfo.BookMemory;
            //    tbPhone.Text = BookInfo.CustTel;
            //    bookNumber.Text = BookInfo.Numbers;

            //    dtpicker.SetTimeDate(BookInfo.ComeTime);
            //    btnBook_Old.Background = Brushes.Orange;

            //}
            //this.BookInfo = BookInfo;
        }
        /// <summary>
        /// /记录刷新
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void MRefresh_Click(object sender, RoutedEventArgs e)
        {
            SetGridInfo();
        }
        /// <summary>
        /// 更多预约日期
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>

        private void btnMoreTime_Click(object sender, RoutedEventArgs e)
        {

        }

        private void usmsinfo_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            btnMsg_Click(null, null);
        }
        private void btnRefund_Click(object sender, RoutedEventArgs e)
        {
            WBrowser browser = new WBrowser("http://www.tang-hui.com.cn/hdProject/refund/Index.html?ApplyName=" + single.vmpc.vmuserinfo.muserinfo.UserName + "&ApplyUserID=" + single.vmpc.vmuserinfo.muserinfo.UserId + "&ApplyShopId=" + single.vmpc.vmuserinfo.muserinfo.ShopId);
            browser.ShowDialog();
        }

        private void MDepositLine_Click(object sender, RoutedEventArgs e)
        {
            MBookCacheInfo book = dgBook.SelectedItem as MBookCacheInfo;
            if (book != null)
            {
                if (book.IsDelete == true) { MessageBox.Show("该预约信息已删除，不可支付定金"); return; }
            }

            WPDeposit_UnLine deposit = new WPDeposit_UnLine(book);
            deposit.ShowDialog();
            if (deposit.IsRefresh == true)
            {
                SetGridInfo();
            }
        }

        private void btnExtend_Click(object sender, RoutedEventArgs e)
        {
            SingleRun.GetSingle().GetWebsite().Open();
        }

        private void TextBlock_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            if (tbPhone.Text == string.Empty) return;
            var rs = MessageBox.Show("确定给" + tbPhone.Text + "发送新在线预订入口短信?", "提示", MessageBoxButton.OKCancel);
            if (rs == MessageBoxResult.OK)
            {
                try
                {
                    int no = 0;
                    if (shopinfoSelect.ShopId == 11)
                    {
                        no = 1;
                    }
                    #region  换取活动连接
                    var responUrl = Json.post("https://r.tang-hui.com.cn/api/miniapp/generate_urllink.ashx?path=pages/rms/onlinebooking/index");
                    var urlLinkData = Json.GetData<UrlLink>(responUrl);
                    if (urlLinkData != null)
                    {

                        #endregion
                        Json.post("http://msg.tang-hui.com.cn/API/Phone/NSendMsg.ashx?no=" + no + "&m=1&phone=" + tbPhone.Text + "&msg=亲爱的先生/女士，欢迎使用在线预订服务，请点击" + urlLinkData.Data.url_link + " 链接进入堂会在线预订小程序，会员使用特权预约自助下单享 5-15元立减优惠，期待您的光临。 拒收请回复R");
                        MessageBox.Show("发送成功");
                    }

                }
                catch (Exception ex)
                {

                    MessageBox.Show("消息发送异常:" + ex.Message);

                }
            }
        }

        private void btnShopHidden_MouseLeftButtonUp(object sender, MouseButtonEventArgs e)
        {
            pshop.Visibility = Visibility.Collapsed;
            btnShopShrink.Visibility = Visibility.Visible;
            btnShopHidden.Visibility = Visibility.Collapsed;
        }

        // private void btnShowBookChart_Click(object sender, RoutedEventArgs e)
        //{
        //    BookChart chart = new BookChart(dgBook.ItemsSource as List<bookcacheinfo_preorder>);
        //    chart.WindowStartupLocation = System.Windows.WindowStartupLocation.CenterScreen;
        //    chart.ShowDialog();
        //}
        /// <summary>
        /// 预订时段统计
        /// </summary>
        public List<UChartConent> SetBookTimeSum()
        {
            List<UChartConent> numberlist = new List<UChartConent>();
            foreach (var item in SingleRun.singleRun.vmpc.vmShopTimeInfo.timeList)
            {
                UChartConent chartConent = new UChartConent() { name = item.TimeName }; ;

                if (item.mBookNumber != null)
                {
                    chartConent.value = item.mBookNumber.BookNoSure - item.mBookNumber.BookSurplus;
                }
                if (chartConent.value != 0)
                {
                    numberlist.Add(chartConent);
                }
            }
            return numberlist;
        }
        /// <summary>
        /// 预订人数统计
        /// </summary>
        //public List<UChartConent> SetBookNumberSum(List<MBookCacheInfo> datalist)
        public List<UChartConent> SetBookNumberSum(List<bookcacheinfo_preorder> datalist)
        {
            List<UChartConent> numberlist = new List<UChartConent>();
            UChartConent chartConent1 = new UChartConent() { name = "1至10人" };
            UChartConent chartConent2 = new UChartConent() { name = "11至15人" };
            UChartConent chartConent3 = new UChartConent() { name = "16至20人" };
            UChartConent chartConent4 = new UChartConent() { name = "20以上" };
            numberlist.Add(chartConent1);
            numberlist.Add(chartConent2);
            numberlist.Add(chartConent3);
            numberlist.Add(chartConent4);
            foreach (var item in datalist)
            {
                if (item.CtNo == 2)
                {
                    if (item.Numbers > 20)
                    {
                        chartConent4.value += item.Numbers;
                    }
                    else if (item.Numbers > 15)
                    {
                        chartConent3.value += item.Numbers;
                    }
                    else if (item.Numbers > 10)
                    {
                        chartConent2.value += item.Numbers;
                    }
                    else if (item.Numbers > 1)
                    {
                        chartConent1.value += item.Numbers;
                    }
                }
            }
            return numberlist;
        }

        private void Button_Click(object sender, RoutedEventArgs e)
        {
            AccessBar bar = new AccessBar();
            bar.WindowStartupLocation = System.Windows.WindowStartupLocation.CenterScreen;
            bar.ShowDialog();
        }

        private void CheckBox_Checked(object sender, RoutedEventArgs e)
        {
            isBookStatusChecked = true; // 勾选时设为 true
        }

        private void tbPhone_Loaded(object sender, RoutedEventArgs e)
        {

        }

        private void CheckBox_Unchecked(object sender, RoutedEventArgs e)
        {
            isBookStatusChecked = false; // 取消勾选时设为 false
        }


    }
}
