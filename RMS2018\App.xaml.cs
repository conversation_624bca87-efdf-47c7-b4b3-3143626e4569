﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Windows;

namespace RMS2018
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        protected override void OnStartup(StartupEventArgs e)
        {
            // 设置全局异常处理
            this.DispatcherUnhandledException += App_DispatcherUnhandledException;
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

            base.OnStartup(e);
        }

        private void App_DispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                string errorMessage = $"应用程序发生未处理的异常:\n{e.Exception.Message}";

                // 检查是否为调试模式
                bool isDebugMode = System.Diagnostics.Debugger.IsAttached;
                if (isDebugMode)
                {
                    errorMessage += $"\n\n详细信息:\n{e.Exception}";
                }

                MessageBox.Show(errorMessage, "错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 记录到调试输出
                System.Diagnostics.Debug.WriteLine($"未处理异常: {e.Exception}");

                // 标记异常已处理，防止应用程序崩溃
                e.Handled = true;
            }
            catch (Exception ex)
            {
                // 如果异常处理本身出错，至少记录到调试输出
                System.Diagnostics.Debug.WriteLine($"异常处理器出错: {ex}");
            }
        }

        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                Exception ex = e.ExceptionObject as Exception;
                string errorMessage = $"应用程序域发生未处理的异常:\n{ex?.Message ?? "未知错误"}";

                MessageBox.Show(errorMessage, "严重错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 记录到调试输出
                System.Diagnostics.Debug.WriteLine($"应用程序域未处理异常: {ex}");
            }
            catch (Exception handlerEx)
            {
                // 如果异常处理本身出错，至少记录到调试输出
                System.Diagnostics.Debug.WriteLine($"应用程序域异常处理器出错: {handlerEx}");
            }
        }
    }
}
