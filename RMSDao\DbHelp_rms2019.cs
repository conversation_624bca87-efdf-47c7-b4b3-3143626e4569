﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Linq;
using System.Text;
using TH_DAO;

namespace RMSDao
{
    public static class DbHelp_rms2019<T>
    {
        public static DAO<T> dao = new DAO<T>();
        /// <summary>
        /// 构造器
        ///    初始化Mysql 链接字符
        /// </summary>
        static DbHelp_rms2019()
        {
            // 直接使用测试服务器地址
            string rmsServerName = "192.168.2.14";

            // 构建连接字符串 - 使用大写的RMS2019数据库名
            dao.connstr = $"Data Source={rmsServerName};Initial Catalog=RMS2019;User ID=sa;Password=***";
        }
    }
}
