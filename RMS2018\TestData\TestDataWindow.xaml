<Window x:Class="RMS2018.TestData.TestDataWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="测试数据管理工具" Height="500" Width="700"
        WindowStartupLocation="CenterScreen">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题和状态 -->
        <StackPanel Grid.Row="0" Margin="0,0,0,10">
            <TextBlock Text="RMS2018 测试数据管理工具" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center"/>
            <TextBlock x:Name="StatusText" Text="状态：未初始化" Margin="0,5,0,0" HorizontalAlignment="Center"/>
        </StackPanel>
        
        <!-- 主要内容 -->
        <TabControl Grid.Row="1">
            <!-- 测试模式控制 -->
            <TabItem Header="测试模式控制">
                <StackPanel Margin="10">
                    <GroupBox Header="测试模式状态" Margin="0,0,0,10">
                        <StackPanel Margin="10">
                            <TextBlock x:Name="TestModeStatus" Text="测试模式：未启用" FontWeight="Bold"/>
                            <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                                <Button x:Name="EnableTestModeBtn" Content="启用测试模式" Width="120" Height="30" Margin="0,0,10,0" Click="EnableTestMode_Click"/>
                                <Button x:Name="DisableTestModeBtn" Content="禁用测试模式" Width="120" Height="30" Margin="0,0,10,0" Click="DisableTestMode_Click"/>
                                <Button x:Name="ResetTestDataBtn" Content="重置测试数据" Width="120" Height="30" Click="ResetTestData_Click"/>
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>
                    
                    <GroupBox Header="数据库连接测试" Margin="0,0,0,10">
                        <StackPanel Margin="10">
                            <TextBlock x:Name="DatabaseStatus" Text="数据库连接：未测试"/>
                            <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                                <Button x:Name="TestConnectionBtn" Content="测试数据库连接" Width="150" Height="30" Click="TestConnection_Click"/>
                                <TextBox x:Name="ConnectionStringText" Width="300" Height="30" Margin="10,0,0,0" 
                                         Text="Data Source=localhost;Initial Catalog=rms2019;User ID=sa;Password="/>
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>
                    
                    <GroupBox Header="快速操作">
                        <StackPanel Margin="10">
                            <Button x:Name="OpenRoomChangeBtn" Content="打开转房界面" Width="150" Height="30" Margin="0,0,0,10" Click="OpenRoomChange_Click"/>
                            <Button x:Name="GenerateTestDataBtn" Content="生成SQL测试数据脚本" Width="150" Height="30" Click="GenerateTestData_Click"/>
                        </StackPanel>
                    </GroupBox>
                </StackPanel>
            </TabItem>
            
            <!-- 测试数据查看 -->
            <TabItem Header="测试数据查看">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                        <Button x:Name="RefreshDataBtn" Content="刷新数据" Width="100" Height="30" Click="RefreshData_Click"/>
                        <ComboBox x:Name="ShopIdCombo" Width="100" Height="30" Margin="10,0,0,0" SelectedIndex="0">
                            <ComboBoxItem Content="门店1" Tag="1"/>
                        </ComboBox>
                    </StackPanel>
                    
                    <DataGrid x:Name="RoomDataGrid" Grid.Row="1" AutoGenerateColumns="False" IsReadOnly="True">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="房号" Binding="{Binding RmNo}" Width="80"/>
                            <DataGridTextColumn Header="房型" Binding="{Binding RtName}" Width="120"/>
                            <DataGridTextColumn Header="状态" Binding="{Binding RmsStatus}" Width="60"/>
                            <DataGridTextColumn Header="客户电话" Binding="{Binding CustTel}" Width="120"/>
                            <DataGridTextColumn Header="账单号" Binding="{Binding InvNo}" Width="120"/>
                            <DataGridTextColumn Header="预约号" Binding="{Binding BookNo}" Width="120"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
            
            <!-- 测试场景 -->
            <TabItem Header="测试场景">
                <StackPanel Margin="10">
                    <GroupBox Header="预设测试场景" Margin="0,0,0,10">
                        <StackPanel Margin="10">
                            <Button x:Name="TestScenario1Btn" Content="场景1：免费升级（标准→豪华）" Width="250" Height="30" Margin="0,0,0,5" Click="TestScenario1_Click"/>
                            <Button x:Name="TestScenario2Btn" Content="场景2：付费升级（标准→总统）" Width="250" Height="30" Margin="0,0,0,5" Click="TestScenario2_Click"/>
                            <Button x:Name="TestScenario3Btn" Content="场景3：同级转房（豪华→豪华）" Width="250" Height="30" Click="TestScenario3_Click"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <GroupBox Header="自定义测试">
                        <Grid Margin="10">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="转出房间：" VerticalAlignment="Center" Margin="0,0,10,5"/>
                            <ComboBox x:Name="FromRoomCombo" Grid.Row="0" Grid.Column="1" Height="25" Margin="0,0,0,5"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="转入房间：" VerticalAlignment="Center" Margin="0,0,10,5"/>
                            <ComboBox x:Name="ToRoomCombo" Grid.Row="1" Grid.Column="1" Height="25" Margin="0,0,0,5"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="计费方式：" VerticalAlignment="Center" Margin="0,0,10,5"/>
                            <StackPanel Grid.Row="2" Grid.Column="1" Orientation="Horizontal" Margin="0,0,0,5">
                                <RadioButton x:Name="KeepRateRadio" Content="保持原房型计费" IsChecked="True" Margin="0,0,20,0"/>
                                <RadioButton x:Name="UpdateRateRadio" Content="按新房型计费"/>
                            </StackPanel>
                            
                            <Button x:Name="CustomTestBtn" Grid.Row="3" Grid.Column="1" Content="执行自定义测试" Width="150" Height="30" HorizontalAlignment="Left" Click="CustomTest_Click"/>
                        </Grid>
                    </GroupBox>
                </StackPanel>
            </TabItem>
        </TabControl>
        
        <!-- 日志输出 -->
        <GroupBox Grid.Row="2" Header="操作日志" Height="120" Margin="0,10,0,0">
            <ScrollViewer>
                <TextBlock x:Name="LogOutput" TextWrapping="Wrap" FontFamily="Consolas" FontSize="10"/>
            </ScrollViewer>
        </GroupBox>
    </Grid>
</Window>
