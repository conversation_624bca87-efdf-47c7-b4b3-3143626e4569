-- =============================================
-- Author:		jjy
-- Create date: 2020-12-18
-- Description:	获取转房数据存储过程
-- Modified:    2025-01-16 - 修复和优化
-- =============================================

-- 首先检查存储过程是否存在，如果存在则删除
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[GetExchangeData]') AND type in (N'P', N'PC'))
DROP PROCEDURE [dbo].[GetExchangeData]
GO

CREATE PROCEDURE [dbo].[GetExchangeData]
	@ShopId int = 0
	--获取转房数据
	--1:获取所有七天内的所有预转及其转房记录
	--2:若7天内无数据则获取所有预转及其转房记录
	--3:若7天内无数据则获取半个月的记录
	--4:若半个月内无数据则获取前一百条转房记录
AS
BEGIN
	SET NOCOUNT ON;
	
	DECLARE @date datetime
	SET @date = GETDATE();
	
	-- 检查是否存在预转记录
	IF EXISTS(SELECT 1 FROM ExchangeWait WHERE ShopId = @ShopId)
	BEGIN
		-- 存在预转记录的情况
		IF EXISTS(SELECT 1 FROM RmExchange WHERE ShopId = @ShopId AND DATEDIFF(dd, ExchangeDate, @date) <= 7)
		BEGIN
			-- 7天内有转房记录
			SELECT *, 1 as IsExchange 
			FROM RmExchange 
			WHERE ShopId = @ShopId AND DATEDIFF(dd, ExchangeDate, @date) <= 7
			UNION ALL
			SELECT id, FromRmNo, ToRmNo, UserName, @date as ExchangeDate, ShopId, 0 as IsExchange 
			FROM ExchangeWait 
			WHERE ShopId = @ShopId 
			ORDER BY ExchangeDate DESC
		END
		ELSE IF EXISTS(SELECT 1 FROM RmExchange WHERE ShopId = @ShopId AND DATEDIFF(dd, ExchangeDate, @date) <= 15)
		BEGIN
			-- 15天内有转房记录
			SELECT *, 1 as IsExchange 
			FROM RmExchange 
			WHERE ShopId = @ShopId AND DATEDIFF(dd, ExchangeDate, @date) <= 15
			UNION ALL
			SELECT id, FromRmNo, ToRmNo, UserName, @date as ExchangeDate, ShopId, 0 as IsExchange 
			FROM ExchangeWait 
			WHERE ShopId = @ShopId 
			ORDER BY ExchangeDate DESC
		END
		ELSE
		BEGIN
			-- 超过15天，获取最近100条记录
			SELECT TOP 100 * FROM
			(
				SELECT *, 1 as IsExchange 
				FROM RmExchange 
				WHERE ShopId = @ShopId
				UNION ALL
				SELECT id, FromRmNo, ToRmNo, UserName, @date as ExchangeDate, ShopId, 0 as IsExchange 
				FROM ExchangeWait 
				WHERE ShopId = @ShopId 
			) b 
			ORDER BY ExchangeDate DESC
		END
	END
	ELSE
	BEGIN
		-- 不存在预转记录的情况
		IF EXISTS(SELECT 1 FROM RmExchange WHERE ShopId = @ShopId AND DATEDIFF(dd, ExchangeDate, @date) <= 7)
		BEGIN
			-- 7天内有转房记录
			SELECT *, 1 as IsExchange 
			FROM RmExchange 
			WHERE ShopId = @ShopId AND DATEDIFF(dd, ExchangeDate, @date) <= 7 
			ORDER BY ExchangeDate DESC
		END
		ELSE IF EXISTS(SELECT 1 FROM RmExchange WHERE ShopId = @ShopId AND DATEDIFF(dd, ExchangeDate, @date) <= 15)
		BEGIN
			-- 15天内有转房记录
			SELECT *, 1 as IsExchange 
			FROM RmExchange 
			WHERE ShopId = @ShopId AND DATEDIFF(dd, ExchangeDate, @date) <= 15 
			ORDER BY ExchangeDate DESC
		END
		ELSE 
		BEGIN
			-- 超过15天，获取最近100条记录
			SELECT TOP 100 *, 1 as IsExchange 
			FROM RmExchange 
			WHERE ShopId = @ShopId 
			ORDER BY ExchangeDate DESC
		END
	END
END
GO

-- 验证存储过程创建成功
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[GetExchangeData]') AND type in (N'P', N'PC'))
BEGIN
    PRINT 'GetExchangeData 存储过程创建成功！'
END
ELSE
BEGIN
    PRINT 'GetExchangeData 存储过程创建失败！'
END

-- 测试存储过程（可选）
-- EXEC GetExchangeData @ShopId = 1
