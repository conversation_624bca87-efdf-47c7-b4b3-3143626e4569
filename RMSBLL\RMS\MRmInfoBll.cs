﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RMSDao.RMS;
using RMSModel.RMS;
using RMSModel.ExtensionRMS;
using RMSModel;

namespace RMSBLL.RMS
{
    public static class MRmInfoBll
    {
        public static MRm_Rt_MArea_MShop GetMRmInfoByInvno(string shopid, string invno)
        {
            return MRmInfoDal.GetMRmInfoByInvno(shopid, invno);
        }



        /// <summary>
        /// 查询MRmInfoDal房间信息表的所有信息（按门店、区域编号、房号排序）
        /// </summary>
        /// <returns></returns>
        public static List<MRm_Rt_MArea_MShop> GetMRmInfo()
        {
            return MRmInfoDal.GetMRmInfo().OrderBy(i => i.ShopId).ThenBy(i => i.AreaId).ThenBy(i => i.RmNo).ToList();
        }
        /// <summary>
        /// 查询MRmInfoDal房间信息表的所有信息
        /// </summary>
        /// <returns></returns>
        public static List<RMSModel.RMS.Api.MRmInfoApi> GetMRmInfo_Api()
        {
            return MRmInfoDal.GetMRmInfo_Api();
        }

        /// <summary>
        /// 根据房号和门店ID查询房间信息
        /// </summary>
        /// <returns></returns>
        public static List<MRm_Rt_MArea_MShop> GetMRmInfo(string RmNo, int ShopId)
        {
            return MRmInfoDal.GetMRmInfo(RmNo, ShopId);
        }

        /// <summary>
        /// 根据根据房号和门店ID查询房间信息(返回对象)
        /// </summary>
        /// <returns></returns>
        public static MRm_Rt_MArea_MShop GetMRmInfoByRmNo(string RmNo, int ShopId)
        {
            return MRmInfoDal.GetMRmInfoByRmNo(RmNo, ShopId);
        }

        /// <summary>
        /// 给MRmInfoDal房间信息表添加一条信息
        /// </summary>
        /// <returns></returns>
        public static int GetMRmInfo_Add(MRmInfo RmInfo)
        {

            return MRmInfoDal.GetMRmInfo_Add(RmInfo);
        }

        /// <summary>
        /// 给MRmInfoDal房间信息表删除一条信息
        /// </summary>
        /// <returns></returns>
        public static int GetMRmInfo_Del(MRmInfo RmInfo)
        {
            return MRmInfoDal.GetMRmInfo_Del(RmInfo);
        }

        /// <summary>
        /// 给MRmInfoDal房间信息表批量删除多条信息
        /// </summary>
        /// <returns>RmNos为1-01,2-02</returns>
        public static int GetMRmInfo_Del(string RmNos)
        {
            string[] id = RmNos.Split(',');
            for (int i = 0; i < id.Length; i++)
            {
                string[] rmno = id[i].Split('-');
                if (rmno.Length > 1)
                {
                    MRmInfoDal.GetMRmInfo_Del(rmno[0], rmno[1]);
                }
            }
            return 1;
        }

        /// <summary>
        /// 给MRmInfoDal房间信息表修改一条信息
        /// </summary>
        /// <returns></returns>
        public static int GetMRmInfo_Udp(string RtUp, string RmNo, string ShopId)
        {
            return MRmInfoDal.GetMRmInfo_Udp(RtUp, RmNo, ShopId);
        }
        /// <summary>
        /// 给MRmInfoDal房间信息表修改一条信息
        /// </summary>
        /// <returns></returns>
        public static int GetMRmInfo_Udp(MRmInfo RmInfo, string oldRmNo, string oldShopId)
        {
            return MRmInfoDal.GetMRmInfo_Udp(RmInfo, oldRmNo, oldShopId);
        }
        /// <summary>
        /// 给MRmInfoDal房间信息表修改一条信息
        /// </summary>
        /// <returns></returns>
        public static int GetMRmInfo_Udp(int shopid, string RmNo, string RmsStatus, string InvNo, string RtUp, string RoomToIkey, string BookNo, string CustTel, bool IsDiscount = false)
        {
            return MRmInfoDal.GetMRmInfo_Udp(shopid, RmNo, RmsStatus, InvNo, RtUp, RoomToIkey, BookNo, CustTel, IsDiscount);
        }

        /// <summary>
        /// 给MRmInfoDal房间信息表修改一条信息(转房)
        /// </summary>
        /// <returns></returns>
        public static int GetMRmInfo_Udp(int shopid, string RmNo, string RtUp, string RoomToIkey, int BillTot, string BookNo, string CustTel, int IsDiscount, string RmsStatus)
        {
            return MRmInfoDal.GetMRmInfo_Udp(shopid, RmNo, RtUp, RoomToIkey, BillTot, BookNo, CustTel, IsDiscount, RmsStatus);
        }

        /// <summary>
        /// 修改房间信息
        /// </summary>
        /// <param name="shopid">门店</param>
        /// <param name="RoomToNo">转入房间</param>
        /// <param name="RoomFromNo">转出房间</param>
        /// <returns></returns>
        public static int GetMRmInfo_Up(int shopid, string RoomToNo, string RoomFromNo, string UserName)
        {
            return MRmInfoDal.GetMRmInfo_Up(shopid, RoomToNo, RoomFromNo, UserName);
        }

        /// <summary>
        /// 修改房间信息（保持原计费房型）
        /// </summary>
        /// <param name="shopid">门店</param>
        /// <param name="RoomToNo">转入房间</param>
        /// <param name="RoomFromNo">转出房间</param>
        /// <param name="UserName">用户名</param>
        /// <returns></returns>
        public static int GetMRmInfo_Up_KeepRate(int shopid, string RoomToNo, string RoomFromNo, string UserName)
        {
            return MRmInfoDal.GetMRmInfo_Up_KeepRate(shopid, RoomToNo, RoomFromNo, UserName);
        }

        public static int SetMRmInfo_Remark(int shopid, string RmNo, string remark)
        {
            return MRmInfoDal.SetMRmInfo_Remark(shopid, RmNo, remark);
            
        }
        public static string GetMRmInfo_Remark(int shopid, string RmNo)
        {
            return MRmInfoDal.GetMRmInfo_Remark(shopid, RmNo);
 
        }
        /// <summary>
        /// 给MRmInfoDal房间信息表修改一条信息
        /// </summary>
        /// <returns></returns>
        public static int GetMRmInfo_Udp(int shopid, string RmNo, string RmsStatus, string InvNo, string RtUp, string remark="")
        {
            return MRmInfoDal.GetMRmInfo_Udp(shopid, RmNo, RmsStatus, InvNo, RtUp, string.Empty, string.Empty, string.Empty, false, remark);
        }

        /// <summary>
        /// 修改状态信息
        /// </summary>
        /// <param name="shopid">门店id</param>
        /// <param name="RmNo">房号</param>
        /// <param name="IsDiscount">折扣</param>
        /// <returns></returns>
        public static int GetMRmInfo_Udp(int shopid, string RmNo, string RmsStatus)
        {
            return MRmInfoDal.GetMRmInfo_Udp(shopid, RmNo, RmsStatus);
        }

        /// <summary>
        /// 修改折扣信息
        /// </summary>
        /// <param name="shopid">门店id</param>
        /// <param name="RmNo">房号</param>
        /// <param name="IsDiscount">折扣</param>
        /// <returns></returns>
        public static int GetMRmInfo_Udp(int shopid, string RmNo, int IsDiscount)
        {
            return MRmInfoDal.GetMRmInfo_Udp(shopid, RmNo, IsDiscount);
        }
        /// <summary>
        /// 更新天王状态到rms2019
        /// </summary>
        /// <param name="shopid">门店id</param>
        /// <param name="RmNo">房号</param>
        /// <param name="RmsStatus">状态</param>
        /// <param name="InvNo">账单号</param>
        /// <param name="DbfoodStatus">天王状态</param>
        /// <returns></returns>
        public static int SetRoomToDbFood(int shopid, string RmNo, string RmsStatus, string InvNo, string DbfoodStatus, int billTot, int IsDiscount)
        {
            return MRmInfoDal.SetRoomToDbFood(shopid, RmNo, RmsStatus, InvNo, DbfoodStatus, billTot, IsDiscount);
        }
        /// <summary>
        /// 更新天王状态到rms2019
        /// </summary>
        /// <param name="shopid">门店id</param>
        /// <param name="RmNo">房号</param>
        /// <param name="RmsStatus">状态</param>
        /// <param name="InvNo">账单号</param>
        /// <param name="DbfoodStatus">天王状态</param>
        /// <returns></returns>
        public static int SetRoomToDbFood(int shopid, string RmNo, string RmsStatus, string InvNo, string DbfoodStatus, int billTot, string RoomToIkey)
        {
            return MRmInfoDal.SetRoomToDbFood(shopid, RmNo, RmsStatus, InvNo, DbfoodStatus, billTot, RoomToIkey);

        }
        /// <summary>
        /// 更新天王状态到rms2019
        /// </summary>
        /// <param name="shopid">门店id</param>
        /// <param name="RmNo">房号</param>
        /// <param name="RmsStatus">状态</param>
        /// <param name="InvNo">账单号</param>
        /// <param name="DbfoodStatus">天王状态</param>
        /// <returns></returns>
        public static int SetRoomToDbFood_kFee(int shopid, string RmNo, string RmsStatus, string InvNo, string DbfoodStatus, int billTot, string RoomToIkey, double kFee)
        {
            return MRmInfoDal.SetRoomToDbFood_kFee(shopid, RmNo, RmsStatus, InvNo, DbfoodStatus, billTot, RoomToIkey, kFee);

        }


        /// <summary>
        /// 更新天王状态到rms2019
        /// </summary>
        /// <param name="shopid">门店id</param>
        /// <param name="RmNo">房号</param>
        /// <param name="RmsStatus">状态</param>
        /// <param name="InvNo">账单号</param>
        /// <param name="DbfoodStatus">天王状态</param>
        public static int SetRoomToDbFood(int shopid, string RmNo, string RmsStatus, string InvNo, string DbfoodStatus, int billTot, string CustTel, int IsDiscount, string BookNo)
        {
            return MRmInfoDal.SetRoomToDbFood(shopid, RmNo, RmsStatus, InvNo, DbfoodStatus, billTot, CustTel, IsDiscount, BookNo);
        }
        /// <summary>
        /// 获取指定分店的所有房间号，根据区域进行分区展示（按门店、区域编号、房号排序）
        /// </summary>
        /// <returns></returns>
        public static List<MRm_Rt_MArea_MShop> GetMRmInfo(int ShopId)
        {
            if (ShopId == 1)
            {
                return MRmInfoDal.GetMRmInfo().OrderBy(i => i.ShopId).ThenBy(i => i.AreaId).ThenBy(i => i.RmNo).ToList();
            }
            else
            {
                return MRmInfoDal.GetMRmInfo(ShopId).OrderBy(i => i.AreaId).ThenBy(i => i.RmNo).ToList();
            }
        }

        /// <summary>
        /// 查询MRmInfoDal房间信息表的所有信息（按门店、房号排序）
        /// </summary>
        /// <returns></returns>
        public static List<MRm_Rt_MArea_MShop> GetMRmInfoByShopid_Rmno(int ShopId)
        {
            if (ShopId == 1)
            {
                return MRmInfoDal.GetMRmInfo().OrderBy(i => i.ShopId).ThenBy(i => i.RmNo).ToList();
            }
            else
            {
                return MRmInfoDal.GetMRmInfo(ShopId).OrderBy(i => i.RmNo).ToList();
            }
        }

        /// <summary>
        /// 查询指定门店的所有不需要同步天王状态的房间
        /// </summary>
        /// <returns></returns>
        public static List<MRmInfoStatus> GetRmInfoNotDbFood(int ShopId)
        {
            return MRmInfoDal.GetRmInfoNotDbFood(ShopId);
        }
        /// <summary>
        /// 查询指定门店的所有不需要同步天王状态的房间如(API专用)
        /// </summary>
        /// <returns></returns>
        public static List<RMSModel.RMS.Api.MRmInfoStatus> GetRmInfoNotDbFoodToAip(string ShopId)
        {
            return MRmInfoDal.GetRmInfoNotDbFoodToAip(ShopId);

        }


        /// <summary>
        /// 查询指定门店的所有不需要同步天王状态的房间如(API专用)
        /// </summary>
        /// <returns></returns>
        public static List<RMSModel.RMS.Api.MRmInfoStatus_v2> GetRmInfoNotDbFoodToAip_v2(string ShopId)
        {
            return MRmInfoDal.GetRmInfoNotDbFoodToAip_v2(ShopId);

        }


        /// <summary>
        /// 查询天王本地房间状态
        /// </summary>
        /// <param name="RmNo">房号</param>
        /// <param name="CustName">顾客姓名</param>
        /// <param name="Number">人数</param>
        /// <returns></returns>
        public static List<MRmInfoStatus> GetRmsRoom()
        {
            return MRmInfoDal.GetRmsRoom();
        }
        /// <summary>
        /// 查询指定门店的所有不需要同步天王状态的房间
        /// </summary>
        /// <returns></returns>
        public static List<MRmInfoStatus> GetRmInfoNotDbFood(int shopid, DateTime time)
        {
            return MRmInfoDal.GetRmInfoNotDbFood(shopid, time);
        }
        /// <summary>
        /// 查询指定门店的所有不需要同步天王状态的房间如
        /// </summary>
        /// <returns></returns>
        public static List<RMSModel.RMS.Api.MRmInfoStatus> GetRmInfoNotDbFood()
        {
            return MRmInfoDal.GetRmInfoNotDbFood();
        }
        /// <summary>
        /// 设置关房时间
        /// </summary>
        /// <param name="ShopId"></param>
        /// <param name="RmNo"></param>
        /// <param name="CloseTime"></param>
        /// <returns></returns>
        public static int SetCloseTime(int ShopId, string RmNo, DateTime CloseTime)
        {
            return MRmInfoDal.SetCloseTime(ShopId, RmNo, CloseTime);
        }
        /// <summary>
        /// 延迟关房
        /// </summary>
        /// <param name="ShopId"></param>
        /// <param name="RmNo"></param>
        /// <param name="delaynumber"></param>
        /// <returns></returns>
        public static int SetDelaynClose(int ShopId, string RmNo, int delaynumber)
        {
            return MRmInfoDal.SetDelaynClose(ShopId, RmNo, delaynumber);

        }

        /// <summary>
        /// 延迟关房
        /// </summary>
        /// <param name="ShopId"></param>
        /// <param name="RmNo"></param>
        /// <param name="delaynumber"></param>
        /// <returns></returns>
        public static int SetDelaynClose(int ShopId, string RmNo, DateTime time)
        {
            return MRmInfoDal.SetDelaynClose(ShopId, RmNo, time);

        }
        /// <summary>
        /// 提前关房
        /// </summary>
        /// <param name="ShopId"></param>
        /// <param name="RmNo"></param>
        /// <param name="delaynumber"></param>
        /// <returns></returns>
        public static int SetNowClose(int ShopId, string RmNo)
        {
            return MRmInfoDal.SetNowClose(ShopId, RmNo);

        }
        /// <summary>
        /// 设置房间状态
        /// </summary>
        /// <param name="ShopId"></param>
        /// <param name="RmNo"></param>
        /// <param name="CloseTime"></param>
        /// <returns></returns>
        public static int SetOpenStatus(int ShopId, string RmNo, string OpenStatus, string ConStatus)
        {
            return MRmInfoDal.SetOpenStatus(ShopId, RmNo, OpenStatus, ConStatus);

        }
        /// <summary>
        /// 获取关房时间
        /// </summary>
        /// <param name="ShopId"></param>
        /// <param name="RmNo"></param>
        /// <returns></returns>
        public static List<RmClose> GetCloseTime(int ShopId)
        {
            return MRmInfoDal.GetCloseTime(ShopId);
        }
        /// <summary>
        /// 数据回绑
        /// </summary>
        /// <returns></returns>
        public static List<MRm_Rt_MArea_MShop> FillMRmInfo(string RmNo, int ShopId)
        {
            return MRmInfoDal.FillMRmInfo(RmNo, ShopId);
        }
        public static List<ExchangeRecord> getExchangeRecord(int ShopId, int newid)
        {
            return MRmInfoDal.getExchangeRecord(ShopId, newid);

        }

    }
}
