using System;
using System.Collections.Generic;
using System.Linq;
using RMSBLL.RMS;
using RMSModel.ExtensionRMS;
using RMSModel.RMS;
using RMSUtils.RMS;

namespace RMS2018
{
    /// <summary>
    /// 测试字典修复的简单控制台程序
    /// </summary>
    public class TestDictionaryFix
    {
        public static void Main(string[] args)
        {
            Console.WriteLine("=== RMS2018 字典修复测试 ===");
            Console.WriteLine();

            try
            {
                int shopId = 1;
                Console.WriteLine($"测试门店ID: {shopId}");
                Console.WriteLine();

                // 1. 测试BillStatus数据
                Console.WriteLine("1. 测试BillStatus数据...");
                var billStatusList = BillStatusBll.GetBillStatus();
                
                if (billStatusList != null && billStatusList.Count > 0)
                {
                    Console.WriteLine($"   获取到账单状态数量: {billStatusList.Count}");
                    foreach (var status in billStatusList)
                    {
                        Console.WriteLine($"     状态: {status.StatusChar} - {status.StatusName}, 背景色: {status.BackgroundColor}, 字体色: {status.FontColor}");
                    }
                }
                else
                {
                    Console.WriteLine("   错误：无法获取账单状态数据");
                }

                Console.WriteLine();

                // 2. 测试VMBillStatus初始化
                Console.WriteLine("2. 测试VMBillStatus初始化...");
                try
                {
                    VMBillStatus vmBstas = new VMBillStatus();
                    var mbStatus = vmBstas.billStatusList;
                    
                    if (mbStatus != null && mbStatus.Count > 0)
                    {
                        Console.WriteLine($"   VMBillStatus获取到状态数量: {mbStatus.Count}");
                    }
                    else
                    {
                        Console.WriteLine("   警告：VMBillStatus.billStatusList为空");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   错误：VMBillStatus初始化失败 - {ex.Message}");
                }

                Console.WriteLine();

                // 3. 测试房间数据和状态匹配
                Console.WriteLine("3. 测试房间数据和状态匹配...");
                var roomList = MRmInfoBll.GetMRmInfoByShopid_Rmno(shopId);
                
                if (roomList != null && roomList.Count > 0)
                {
                    var filteredRooms = roomList.FindAll(i => i.IsDisplay == false).ToList();
                    if (filteredRooms.Count == 0)
                    {
                        filteredRooms = roomList.ToList();
                    }

                    Console.WriteLine($"   房间数量: {filteredRooms.Count}");
                    
                    // 检查房间状态是否在BillStatus中存在
                    var roomStatuses = filteredRooms.Select(r => r.RmsStatus).Distinct().ToList();
                    var billStatuses = billStatusList?.Select(b => b.StatusChar).ToList() ?? new List<string>();
                    
                    Console.WriteLine("   房间状态检查:");
                    foreach (var roomStatus in roomStatuses)
                    {
                        bool exists = billStatuses.Contains(roomStatus);
                        Console.WriteLine($"     状态 '{roomStatus}': {(exists ? "✅ 存在于BillStatus" : "❌ 不存在于BillStatus")}");
                    }
                    
                    // 显示房间详情
                    Console.WriteLine("   房间详情:");
                    foreach (var room in filteredRooms.Take(5))
                    {
                        Console.WriteLine($"     房号: {room.RmNo}, 状态: {room.RmsStatus}, 区域: {room.AreaName}");
                    }
                }
                else
                {
                    Console.WriteLine("   错误：无法获取房间数据");
                }

                Console.WriteLine();

                // 4. 测试区域数据
                Console.WriteLine("4. 测试区域数据...");
                if (roomList != null && roomList.Count > 0)
                {
                    var filteredRooms = roomList.FindAll(i => i.IsDisplay == false).ToList();
                    if (filteredRooms.Count == 0)
                    {
                        filteredRooms = roomList.ToList();
                    }

                    var areas = filteredRooms.GroupBy(r => r.AreaName)
                        .Where(g => !string.IsNullOrEmpty(g.Key))
                        .Select(r => r.First()).ToList();
                        
                    Console.WriteLine($"   区域数量: {areas.Count}");
                    foreach (var area in areas)
                    {
                        var roomsInArea = filteredRooms.Where(r => r.AreaName == area.AreaName).Count();
                        Console.WriteLine($"     区域: {area.AreaName}, 房间数: {roomsInArea}");
                    }
                }

                Console.WriteLine();
                Console.WriteLine("=== 测试完成 ===");
                
                // 总结
                bool hasValidBillStatus = billStatusList != null && billStatusList.Count > 0;
                bool hasValidRooms = roomList != null && roomList.Count > 0;
                
                if (hasValidBillStatus && hasValidRooms)
                {
                    Console.WriteLine("✅ 字典初始化应该能够正常工作");
                    Console.WriteLine("✅ 应用程序应该不再出现'给定关键字不在字典中'错误");
                }
                else
                {
                    Console.WriteLine("❌ 仍有数据问题，可能需要进一步检查");
                    if (!hasValidBillStatus) Console.WriteLine("   - BillStatus数据缺失");
                    if (!hasValidRooms) Console.WriteLine("   - 房间数据缺失");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试过程中发生错误: {ex.Message}");
                Console.WriteLine($"   堆栈跟踪: {ex.StackTrace}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
